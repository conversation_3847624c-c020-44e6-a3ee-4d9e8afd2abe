
import sqlite3

def get_db_connection():
    """建立并返回数据库连接"""
    conn = sqlite3.connect('weather_data.db')
    conn.row_factory = sqlite3.Row
    return conn

def create_tables():
    """创建所有数据表"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # 气压 (Pressure)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS hourly_pressure (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT NOT NULL,
        year INTEGER NOT NULL,
        month INTEGER NOT NULL,
        day INTEGER NOT NULL,
        hour INTEGER NOT NULL,
        pressure REAL,
        UNIQUE(station_id, year, month, day, hour)
    )""")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS daily_pressure (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT NOT NULL,
        year INTEGER NOT NULL,
        month INTEGER NOT NULL,
        day INTEGER NOT NULL,
        max_pressure REAL,
        min_pressure REAL,
        UNIQUE(station_id, year, month, day)
    )""")

    # 气温 (Temperature)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS hourly_temperature (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT NOT NULL,
        year INTEGER NOT NULL,
        month INTEGER NOT NULL,
        day INTEGER NOT NULL,
        hour INTEGER NOT NULL,
        temperature REAL,
        UNIQUE(station_id, year, month, day, hour)
    )""")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS daily_temperature (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT NOT NULL,
        year INTEGER NOT NULL,
        month INTEGER NOT NULL,
        day INTEGER NOT NULL,
        max_temperature REAL,
        min_temperature REAL,
        UNIQUE(station_id, year, month, day)
    )""")

    # 相对湿度 (Humidity)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS hourly_humidity (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER, hour INTEGER,
        humidity INTEGER
    )""")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS daily_humidity (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER,
        min_humidity INTEGER
    )""")

    # 降水量 (Precipitation)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS hourly_precipitation (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER, hour INTEGER,
        precipitation REAL
    )""")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS daily_precipitation (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER,
        total_precipitation REAL
    )""")

    # 风 (Wind)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS hourly_wind (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER, hour INTEGER,
        wind_direction TEXT, wind_speed REAL
    )""")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS daily_wind (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER,
        max_wind_speed REAL, extreme_wind_speed REAL
    )""")

    # 湿球温度 (Wet Bulb Temperature)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS hourly_wet_bulb_temperature (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER, hour INTEGER,
        wet_bulb_temperature REAL
    )""")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS daily_wet_bulb_temperature (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER,
        wet_bulb_temperature REAL
    )""")

    # 水汽压 (Vapor Pressure)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS hourly_vapor_pressure (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER, hour INTEGER,
        vapor_pressure REAL
    )""")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS daily_vapor_pressure (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER,
        vapor_pressure REAL
    )""")

    # 能见度 (Visibility)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS hourly_visibility (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER, hour INTEGER,
        visibility REAL
    )""")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS daily_visibility (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER,
        min_visibility REAL
    )""")

    # 日照时数 (Sunshine Hours)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS hourly_sunshine_hours (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER, hour INTEGER,
        sunshine_hours REAL
    )""")
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS daily_sunshine_hours (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        station_id TEXT, year INTEGER, month INTEGER, day INTEGER,
        total_sunshine_hours REAL
    )""")

    # 创建索引以提高查询性能
    create_indexes(cursor)

    conn.commit()
    conn.close()

def create_indexes(cursor):
    """创建数据库索引"""
    indexes = [
        # 小时数据索引
        "CREATE INDEX IF NOT EXISTS idx_hourly_pressure_station_time ON hourly_pressure(station_id, year, month, day, hour)",
        "CREATE INDEX IF NOT EXISTS idx_hourly_temperature_station_time ON hourly_temperature(station_id, year, month, day, hour)",
        "CREATE INDEX IF NOT EXISTS idx_hourly_humidity_station_time ON hourly_humidity(station_id, year, month, day, hour)",
        "CREATE INDEX IF NOT EXISTS idx_hourly_precipitation_station_time ON hourly_precipitation(station_id, year, month, day, hour)",
        "CREATE INDEX IF NOT EXISTS idx_hourly_wind_station_time ON hourly_wind(station_id, year, month, day, hour)",

        # 日数据索引
        "CREATE INDEX IF NOT EXISTS idx_daily_pressure_station_time ON daily_pressure(station_id, year, month, day)",
        "CREATE INDEX IF NOT EXISTS idx_daily_temperature_station_time ON daily_temperature(station_id, year, month, day)",
        "CREATE INDEX IF NOT EXISTS idx_daily_humidity_station_time ON daily_humidity(station_id, year, month, day)",
        "CREATE INDEX IF NOT EXISTS idx_daily_precipitation_station_time ON daily_precipitation(station_id, year, month, day)",
        "CREATE INDEX IF NOT EXISTS idx_daily_wind_station_time ON daily_wind(station_id, year, month, day)",

        # 站点索引
        "CREATE INDEX IF NOT EXISTS idx_station_id ON hourly_pressure(station_id)",
        "CREATE INDEX IF NOT EXISTS idx_time ON hourly_pressure(year, month, day)"
    ]

    for index_sql in indexes:
        try:
            cursor.execute(index_sql)
        except Exception as e:
            print(f"创建索引时出错: {e}")

def create_views():
    """创建常用的数据库视图"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # 创建月度统计视图
    cursor.execute("""
    CREATE VIEW IF NOT EXISTS monthly_temperature_stats AS
    SELECT
        station_id,
        year,
        month,
        AVG(temperature) as avg_temperature,
        MIN(temperature) as min_temperature,
        MAX(temperature) as max_temperature,
        COUNT(*) as record_count
    FROM hourly_temperature
    GROUP BY station_id, year, month
    """)

    cursor.execute("""
    CREATE VIEW IF NOT EXISTS monthly_precipitation_stats AS
    SELECT
        station_id,
        year,
        month,
        SUM(total_precipitation) as total_precipitation,
        AVG(total_precipitation) as avg_daily_precipitation,
        MAX(total_precipitation) as max_daily_precipitation,
        COUNT(*) as days_with_data
    FROM daily_precipitation
    GROUP BY station_id, year, month
    """)

    conn.commit()
    conn.close()

if __name__ == '__main__':
    create_tables()
    create_views()
    print("数据库、数据表和视图创建成功。")
