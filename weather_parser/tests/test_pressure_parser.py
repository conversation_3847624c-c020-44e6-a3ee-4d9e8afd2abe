"""
气压数据解析器测试模块
"""

import pytest
from pathlib import Path

from weather_parser.parsers.pressure_parser import PressureParser
from weather_parser.models import ElementData, ParseContext
from weather_parser.exceptions import DataValidationError, FileFormatError


class TestPressureParser:
    """PressureParser测试类"""
    
    def setup_method(self):
        """设置测试环境"""
        self.parser = PressureParser()
        self.context = ParseContext(file_path=Path("test.txt"))
    
    def test_parse_mode_c_data(self):
        """测试解析方式位C的气压数据"""
        lines = [
            "0160 0158 0160 0156 0152 0151 0151 0150 0151 0147 0146 0143",
            "0143 0143 0135 0125 0112 0102 0097 0093 0091 0093 0091 0090 0162 2257 0090 1628."
        ]
        
        element_data = self.parser.parse(lines, "C", self.context)
        
        assert element_data.element_code == "P"
        assert element_data.element_name == "气压"
        assert element_data.mode == "C"
        assert len(element_data.segments) >= 2  # 两行数据 + 摘要
        
        # 检查第一行数据
        first_segment = element_data.segments[0]
        assert first_segment['line_number'] == 1
        assert len(first_segment['pressure_values']) == 12
        assert first_segment['pressure_values'][0] == pytest.approx(1016.0, abs=0.1)  # 0160 -> 1016.0hPa
        assert first_segment['valid_count'] == 12
        assert first_segment['missing_count'] == 0
        
        # 检查第二行数据（包含极值）
        second_segment = element_data.segments[1]
        assert second_segment['line_number'] == 2
        assert 'extremes' in second_segment
        extremes = second_segment['extremes']
        assert extremes['max_pressure'] == pytest.approx(1016.2, abs=0.1)  # 0162 -> 1016.2hPa
        assert extremes['max_time'] == "22:57"
        assert extremes['min_pressure'] == pytest.approx(1009.0, abs=0.1)  # 0090 -> 1009.0hPa
        assert extremes['min_time'] == "16:28"
    
    def test_convert_pressure_value(self):
        """测试气压值转换"""
        # 正常值转换（本站气压值，实际A文件中的格式）
        assert self.parser._convert_pressure_value("0160") == pytest.approx(1016.0, abs=0.1)  # 1016.0hPa
        assert self.parser._convert_pressure_value("0090") == pytest.approx(1009.0, abs=0.1)  # 1009.0hPa  
        assert self.parser._convert_pressure_value("9950") == pytest.approx(995.0, abs=0.1)   # 995.0hPa
        
        # 海平面气压值（小值需要加1000）
        assert self.parser._convert_pressure_value("0013") == pytest.approx(1001.3, abs=0.1)
        assert self.parser._convert_pressure_value("0250") == pytest.approx(1025.0, abs=0.1)
        
        # 缺测值
        assert self.parser._convert_pressure_value("9999") is None
        assert self.parser._convert_pressure_value("////") is None
        assert self.parser._convert_pressure_value("9996") is None
        assert self.parser._convert_pressure_value("") is None
        
        # 异常值
        assert self.parser._convert_pressure_value("5000") is None  # 超出合理范围
        assert self.parser._convert_pressure_value("0001") == pytest.approx(1000.1, abs=0.1)  # 边界值
    
    def test_parse_time(self):
        """测试时间解析"""
        # 正常时间
        assert self.parser._parse_time("2257") == "22:57"
        assert self.parser._parse_time("1628") == "16:28"
        assert self.parser._parse_time("0000") == "00:00"
        assert self.parser._parse_time("2359") == "23:59"
        
        # 无效时间
        assert self.parser._parse_time("2560") is None  # 无效分钟
        assert self.parser._parse_time("2500") is None  # 无效小时
        assert self.parser._parse_time("123") is None   # 格式错误
        assert self.parser._parse_time("") is None      # 空字符串
    
    def test_mode_mapping(self):
        """测试方式位映射"""
        # 测试所有支持的方式位
        for mode in ['C', '3', '4', '6', '8', 'B', 'D']:
            lines = ["1013 1014 1015 1016"]
            element_data = self.parser.parse(lines, mode, self.context)
            assert element_data.mode == mode
            assert len(element_data.segments) >= 1
    
    def test_unsupported_mode(self):
        """测试不支持的方式位"""
        lines = ["1013 1014 1015 1016"]
        
        with pytest.raises(DataValidationError, match="不支持的气压方式位"):
            self.parser.parse(lines, "X", self.context)
    
    def test_parse_data_with_missing_values(self):
        """测试包含缺测值的数据解析"""
        lines = [
            "0160 9999 0160 //// 0152 0151",
            "9996 0143 0135 9990 0112 0102"
        ]
        
        element_data = self.parser.parse(lines, "C", self.context)
        
        # 检查第一行
        first_segment = element_data.segments[0]
        pressure_values = first_segment['pressure_values']
        assert pressure_values[0] == pytest.approx(1016.0, abs=0.1)  # 0160
        assert pressure_values[1] is None  # 9999 (缺测)
        assert pressure_values[2] == pytest.approx(1016.0, abs=0.1)  # 0160
        assert pressure_values[3] is None  # //// (缺测)
        assert first_segment['valid_count'] == 4
        assert first_segment['missing_count'] == 2
    
    def test_empty_lines(self):
        """测试空行处理"""
        lines = [
            "0160 0158 0160",
            "",
            "   ",
            "0143 0143 0135"
        ]
        
        element_data = self.parser.parse(lines, "C", self.context)
        
        # 应该只有2个有效段（排除空行） + 1个摘要段
        data_segments = [s for s in element_data.segments if not s.get('summary', False)]
        assert len(data_segments) == 2
    
    def test_extreme_values_parsing(self):
        """测试极值解析"""
        lines = [
            "0143 0143 0135 0125 0112 0102 0097 0093 0091 0093 0091 0090 0162 2257 0090 1628."
        ]
        
        element_data = self.parser.parse(lines, "C", self.context)
        
        segment = element_data.segments[0]
        assert 'extremes' in segment
        
        extremes = segment['extremes']
        assert extremes['max_pressure'] == pytest.approx(1016.2, abs=0.1)
        assert extremes['max_time'] == "22:57"
        assert extremes['min_pressure'] == pytest.approx(1009.0, abs=0.1)
        assert extremes['min_time'] == "16:28"
    
    def test_data_validation(self):
        """测试数据验证"""
        # 正常数据
        lines = ["1013 1014 1015 1016"]
        element_data = self.parser.parse(lines, "C", self.context)
        # 这些值实际会转换为101.3, 101.4等，小于800hPa，会触发警告
        warnings = [w for w in element_data.warnings if "异常气压值" in w or "完整性较低" in w]
        # 由于值太小，可能有警告
        
        # 异常数据（压力值过高）
        lines = ["9000 8000 7000 6000"]  # 包含过大异常值
        element_data = self.parser.parse(lines, "C", self.context)
        
        # 应该有警告信息或缺测值处理
        warnings = [w for w in element_data.warnings if "异常气压值" in w or "完整性较低" in w]
        assert len(warnings) >= 0  # 至少不会崩溃
    
    def test_data_completeness_warning(self):
        """测试数据完整性警告"""
        # 大部分缺测的数据
        lines = [
            "9999 9999 9999 0160",
            "9999 9999 9999 9999"
        ]
        
        element_data = self.parser.parse(lines, "C", self.context)
        
        # 应该有完整性警告
        completeness_warnings = [w for w in element_data.warnings if "完整性较低" in w]
        assert len(completeness_warnings) > 0
    
    def test_summary_segment(self):
        """测试摘要段生成"""
        lines = [
            "0160 0158 0160 0156",
            "0143 0143 0135 0125"
        ]
        
        element_data = self.parser.parse(lines, "C", self.context)
        
        # 查找摘要段
        summary_segments = [s for s in element_data.segments if s.get('summary', False)]
        assert len(summary_segments) == 1
        
        summary = summary_segments[0]
        assert summary['total_values'] == 8
        assert summary['valid_values'] == 8
        assert summary['completeness_percent'] == 100.0
        assert 'mode_description' in summary
    
    def test_real_data_format(self):
        """测试真实数据格式"""
        # 使用真实的A文件气压数据格式
        real_lines = [
            "0160 0158 0160 0156 0152 0151 0151 0150 0151 0147 0146 0143",
            "0143 0143 0135 0125 0112 0102 0097 0093 0091 0093 0091 0090 0162 2257 0090 1628.",
            "0085 0083 0080 0079 0075 0072 0067 0064 0068 0073 0081 0088"
        ]
        
        element_data = self.parser.parse(real_lines, "C", self.context)
        
        # 验证解析结果
        assert element_data.element_code == "P"
        assert element_data.mode == "C"
        
        # 至少有3个数据段 + 1个摘要段
        data_segments = [s for s in element_data.segments if not s.get('summary', False)]
        assert len(data_segments) == 3
        
        # 验证具体数值转换
        first_segment = data_segments[0]
        assert first_segment['pressure_values'][0] == pytest.approx(1016.0, abs=0.1)
        assert first_segment['pressure_values'][-1] == pytest.approx(1014.3, abs=0.1)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试解析错误的累积
        lines = [
            "0160 0158 0160",  # 正常行
            "invalid line format",  # 可能导致错误的行
            "0143 0143 0135"   # 正常行
        ]
        
        element_data = self.parser.parse(lines, "C", self.context)
        
        # 应该有至少2个有效段（正常行）
        data_segments = [s for s in element_data.segments if not s.get('summary', False)]
        assert len(data_segments) >= 2
        
        # 错误应该被记录但不影响其他行的解析
        if element_data.errors:
            assert any("invalid line format" in error for error in element_data.errors)


if __name__ == "__main__":
    pytest.main([__file__]) 