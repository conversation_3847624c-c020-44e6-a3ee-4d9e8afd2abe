"""
质量控制信息解析器模块

处理质量控制码和更正数据段的解析功能。
实现三级质量控制、更正数据记录和质量评估机制。
"""

import re
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime, time

from ..models import ElementData, ParseContext
from ..exceptions import DataValidationError, FileFormatError
from ..constants import MISSING_VALUE_INDICATORS, QualityCode, QUALITY_LEVELS
from loguru import logger


class QualityControlParser:
    """质量控制信息解析器"""
    
    def __init__(self):
        """初始化解析器"""
        self.logger = logger.bind(parser="QualityControlParser")
        
        # 支持的质量控制段类型
        self.qc_types = {
            "QC": "质量控制码段",
            "CR": "更正数据段",
            "QA": "质量评估段",
            "AD": "附加信息段"
        }
        
        # 质量控制级别
        self.quality_levels = QUALITY_LEVELS
        
        # 质量控制码（从枚举中提取）
        self.quality_codes = {code.name: code.value for code in QualityCode}
        
        # 更正类型映射
        self.correction_types = {
            "A": "自动订正",
            "M": "人工订正", 
            "S": "系统订正",
            "Q": "质量控制订正",
            "T": "临时订正",
            "F": "最终订正",
            "C": "取消订正",
            "D": "删除数据"
        }
        
        # 质量标记
        self.quality_flags = {
            "0": "正常",
            "1": "可疑",
            "2": "错误",
            "3": "缺测",
            "4": "超限",
            "5": "异常",
            "6": "插补",
            "7": "估算",
            "8": "人工",
            "9": "其他"
        }
    
    def _parse_qc_code(self, qc_str: str) -> Optional[Dict[str, Any]]:
        """
        解析质量控制码
        
        Args:
            qc_str: 质量控制码字符串
            
        Returns:
            质量控制信息字典或None
        """
        if not qc_str or qc_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        qc_str = qc_str.strip()
        
        try:
            # 标准格式：级别+代码（如2A表示二级质量控制代码A）
            if len(qc_str) == 2 and qc_str[0].isdigit() and qc_str[1].isalpha():
                level = qc_str[0]
                code = qc_str[1]
                
                level_info = self.quality_levels.get(level)
                code_info = self.quality_codes.get(code)
                
                if level_info and code_info:
                    return {
                        "level": level,
                        "level_name": level_info,
                        "code": code,
                        "code_name": code_info,
                        "full_code": qc_str,
                        "description": f"{level_info}-{code_info}"
                    }
            
            # 简单标记格式
            if qc_str in self.quality_flags:
                return {
                    "flag": qc_str,
                    "flag_name": self.quality_flags[qc_str],
                    "full_code": qc_str,
                    "description": self.quality_flags[qc_str]
                }
                
        except (ValueError, KeyError):
            pass
            
        # 如果是缺测值，返回None
        if qc_str in ["///", "//", ""]:
            return None
            
        return {
            "unknown_code": qc_str,
            "description": f"未知质量控制码: {qc_str}"
        }
    
    def _parse_correction_record(self, record_str: str) -> Optional[Dict[str, Any]]:
        """
        解析更正记录
        
        Args:
            record_str: 更正记录字符串
            
        Returns:
            更正记录信息字典或None
        """
        if not record_str or record_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        record_str = record_str.strip()
        
        # 更正记录格式：类型+位置+原值+新值[+时间]
        # 示例：M12345678900120230615 表示人工订正第12345678位置原值900新值120时间230615
        try:
            if len(record_str) >= 14:  # 最小长度检查
                correction_type = record_str[0]
                position = record_str[1:9]
                old_value = record_str[9:12]
                
                if len(record_str) >= 20:  # 长格式：包含时间戳
                    new_value = record_str[12:15]
                    timestamp_raw = record_str[15:]
                    # 如果时间戳是6位，前面补上"20"变成8位完整日期
                    if len(timestamp_raw) == 6 and timestamp_raw.isdigit():
                        timestamp = "20" + timestamp_raw
                    else:
                        timestamp = timestamp_raw
                elif len(record_str) >= 15:  # 中格式
                    new_value = record_str[12:15]
                    timestamp = None
                else:  # 短格式：只有新值的前两位
                    new_value = record_str[12:]
                    timestamp = None
                
                return {
                    "type": correction_type,
                    "type_name": self.correction_types.get(correction_type, f"类型{correction_type}"),
                    "position": position,
                    "old_value": old_value,
                    "new_value": new_value,
                    "timestamp": timestamp,
                    "raw_record": record_str
                }
                
        except (ValueError, IndexError):
            pass
            
        return {
            "raw_record": record_str,
            "description": f"无法解析的更正记录: {record_str}"
        }
    
    def _parse_quality_statistics(self, stats_str: str) -> Optional[Dict[str, Any]]:
        """
        解析质量统计信息
        
        Args:
            stats_str: 质量统计字符串
            
        Returns:
            质量统计信息字典或None
        """
        if not stats_str or stats_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        stats_str = stats_str.strip()
        
        try:
            # 统计格式：总数+正常+可疑+错误+缺测（各占2位）
            if len(stats_str) >= 10 and stats_str.isdigit():
                total = int(stats_str[0:2])
                normal = int(stats_str[2:4])
                suspect = int(stats_str[4:6])
                error = int(stats_str[6:8])
                missing = int(stats_str[8:10])
                
                return {
                    "total_count": total,
                    "normal_count": normal,
                    "suspect_count": suspect,
                    "error_count": error,
                    "missing_count": missing,
                    "quality_rate": (normal / total * 100) if total > 0 else 0,
                    "completeness_rate": ((total - missing) / total * 100) if total > 0 else 0
                }
                
        except (ValueError, ZeroDivisionError):
            pass
            
        return None
    
    def _parse_qc_line(self, line: str, qc_type: str) -> Dict[str, Any]:
        """
        解析质量控制数据行
        
        Args:
            line: 数据行
            qc_type: 质量控制类型
            
        Returns:
            解析结果字典
        """
        values = line.strip().split()
        result = {
            "raw_line": line,
            "qc_type": qc_type,
            "type_description": self.qc_types.get(qc_type, f"类型{qc_type}"),
            "qc_data": [],
            "valid_count": 0,
            "missing_count": 0
        }
        
        if qc_type == "QC":  # 质量控制码段
            for value in values:
                qc_info = self._parse_qc_code(value)
                if qc_info:
                    result["qc_data"].append(qc_info)
                    result["valid_count"] += 1  # 所有非None的都算有效，包括unknown_code
                else:
                    result["missing_count"] += 1
        
        elif qc_type == "CR":  # 更正数据段
            for value in values:
                correction_info = self._parse_correction_record(value)
                if correction_info:
                    result["qc_data"].append(correction_info)
                    if "raw_record" in correction_info and "type" in correction_info:
                        result["valid_count"] += 1
                    else:
                        result["missing_count"] += 1
                else:
                    result["missing_count"] += 1
        
        elif qc_type == "QA":  # 质量评估段
            for value in values:
                stats_info = self._parse_quality_statistics(value)
                if stats_info:
                    result["qc_data"].append(stats_info)
                    result["valid_count"] += 1
                else:
                    result["missing_count"] += 1
        
        else:  # 其他附加信息
            for value in values:
                if value == "=" or "连接符" in value:  # 连接符号
                    result["has_connection"] = True
                    # 不将连接符加入数据列表
                    continue
                    
                # 按文本信息处理
                info_item = {
                    "content": value,
                    "raw_value": value
                }
                result["qc_data"].append(info_item)
                result["valid_count"] += 1
        
        return result
    
    def parse(self, lines: List[str], qc_type: str, mode: str, context: ParseContext) -> ElementData:
        """
        解析质量控制信息
        
        Args:
            lines: 数据行列表
            qc_type: 质量控制类型（QC/CR/QA/AD）
            mode: 方式位（通常为0）
            context: 解析上下文
            
        Returns:
            解析结果
        """
        self.logger.debug(f"开始解析{qc_type}质量控制数据，方式位: {mode}, 行数: {len(lines)}")
        
        # 验证质量控制类型
        if qc_type not in self.qc_types:
            raise DataValidationError(f"不支持的质量控制类型: {qc_type}")
        
        type_name = self.qc_types[qc_type]
        
        result = ElementData(
            element_code=qc_type,
            element_name=type_name,
            mode=mode,
            segments=[],
            quality_codes=None,
            errors=[],
            warnings=[]
        )
        
        # 统计信息
        total_values = 0
        valid_values = 0
        data_segments = []
        
        # 解析每行数据
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # 跳过空行和分隔行
            if not line or line in ["=", "==", "==="] or line.isspace():
                continue
            
            try:
                segment = self._parse_qc_line(line, qc_type)
                segment["line_number"] = line_num
                data_segments.append(segment)
                
                total_values += segment["valid_count"] + segment["missing_count"]
                valid_values += segment["valid_count"]
                
            except Exception as e:
                error_msg = f"第{line_num}行解析错误: {str(e)}"
                result.errors.append(error_msg)
                self.logger.error(error_msg)
        
        # 添加数据段
        result.segments.extend(data_segments)
        
        # 数据完整性检查
        if total_values > 0:
            completeness = (valid_values / total_values) * 100
            if completeness < 90:  # 质量控制信息要求更高完整性
                warning = f"{type_name}数据完整性较低: {completeness:.1f}%"
                result.warnings.append(warning)
        
        # 质量控制特殊验证
        self._validate_qc_data(result, qc_type)
        
        # 生成质量控制汇总段
        if data_segments:
            qc_summary = self._generate_qc_summary(data_segments, qc_type)
            result.segments.append(qc_summary)
        
        if not data_segments:
            result.warnings.append(f"未找到有效的{type_name}数据段")
        
        self.logger.info(f"{type_name}数据解析完成: 方式位{mode}, 有效段数{len(data_segments)}, 错误数{len(result.errors)}")
        
        return result
    
    def _validate_qc_data(self, result: ElementData, qc_type: str) -> None:
        """验证质量控制数据"""
        if qc_type == "QC":  # 质量控制码验证
            unknown_codes = []
            for segment in result.segments:
                if "qc_data" in segment:
                    for qc_item in segment["qc_data"]:
                        if "unknown_code" in qc_item:
                            unknown_codes.append(qc_item["unknown_code"])
            
            if unknown_codes:
                result.warnings.append(f"发现未知质量控制码: {', '.join(set(unknown_codes))}")
        
        elif qc_type == "CR":  # 更正记录验证
            correction_count = 0
            for segment in result.segments:
                if "qc_data" in segment:
                    correction_count += len([item for item in segment["qc_data"] if "type" in item])
            
            if correction_count > 0:
                result.warnings.append(f"数据包含{correction_count}条更正记录")
        
        elif qc_type == "QA":  # 质量评估验证
            for segment in result.segments:
                if "qc_data" in segment:
                    for stats_item in segment["qc_data"]:
                        if "quality_rate" in stats_item:
                            quality_rate = stats_item["quality_rate"]
                            if quality_rate < 80:
                                result.warnings.append(f"数据质量率较低: {quality_rate:.1f}%")
                            
                            completeness_rate = stats_item.get("completeness_rate", 0)
                            if completeness_rate < 90:
                                result.warnings.append(f"数据完整性较低: {completeness_rate:.1f}%")
    
    def _generate_qc_summary(self, data_segments: List[Dict[str, Any]], qc_type: str) -> Dict[str, Any]:
        """生成质量控制汇总信息"""
        summary = {
            "summary": True,
            "qc_type": qc_type,
            "type_description": self.qc_types[qc_type],
            "total_segments": len(data_segments),
            "total_items": sum(len(seg.get("qc_data", [])) for seg in data_segments),
            "valid_items": sum(seg.get("valid_count", 0) for seg in data_segments),
            "missing_items": sum(seg.get("missing_count", 0) for seg in data_segments)
        }
        
        if qc_type == "QC":  # 质量控制码汇总
            level_counts = {}
            code_counts = {}
            
            for segment in data_segments:
                for qc_item in segment.get("qc_data", []):
                    if "level" in qc_item:
                        level = qc_item["level"]
                        level_counts[level] = level_counts.get(level, 0) + 1
                    
                    if "code" in qc_item:
                        code = qc_item["code"]
                        code_counts[code] = code_counts.get(code, 0) + 1
            
            summary.update({
                "level_distribution": level_counts,
                "code_distribution": code_counts,
                "quality_control_applied": len(level_counts) > 0
            })
        
        elif qc_type == "CR":  # 更正记录汇总
            type_counts = {}
            
            for segment in data_segments:
                for correction_item in segment.get("qc_data", []):
                    if "type" in correction_item:
                        corr_type = correction_item["type"]
                        type_counts[corr_type] = type_counts.get(corr_type, 0) + 1
            
            summary.update({
                "correction_type_distribution": type_counts,
                "corrections_applied": sum(type_counts.values())
            })
        
        elif qc_type == "QA":  # 质量评估汇总
            total_quality_rate = 0
            total_completeness_rate = 0
            stats_count = 0
            
            for segment in data_segments:
                for stats_item in segment.get("qc_data", []):
                    if "quality_rate" in stats_item:
                        total_quality_rate += stats_item["quality_rate"]
                        total_completeness_rate += stats_item.get("completeness_rate", 0)
                        stats_count += 1
            
            if stats_count > 0:
                summary.update({
                    "average_quality_rate": total_quality_rate / stats_count,
                    "average_completeness_rate": total_completeness_rate / stats_count,
                    "quality_assessment_count": stats_count
                })
        
        return summary 