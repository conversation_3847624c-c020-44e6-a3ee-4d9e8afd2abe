"""
风要素和地温解析器模块

处理风向风速和地温（浅层、深层）数据的解析功能。
支持2分钟平均风、10分钟平均风、极大风以及多层地温数据。
"""

import re
from typing import Dict, List, Optional, Union, Any
from datetime import time

from ..models import ElementData, ParseContext
from ..exceptions import DataValidationError, FileFormatError
from ..constants import MISSING_VALUE_INDICATORS, WIND_DIRECTIONS_16, WIND_DIRECTIONS_8
from loguru import logger


class WindTemperatureParser:
    """风要素和地温解析器"""
    
    def __init__(self):
        """初始化解析器"""
        self.logger = logger.bind(parser="WindTemperatureParser")
        
        # 支持的要素代码
        self.supported_elements = {
            "F": "风要素",
            "D": "浅层地温", 
            "K": "深层地温"
        }
        
        # 风要素支持的方式位
        self.wind_modes = {
            "N": "2分钟平均风",
            "1": "10分钟平均风",
            "2": "极大风及时间",
            "3": "瞬间风向风速",
            "4": "阵风风向风速"
        }
        
        # 地温要素支持的方式位
        self.temperature_modes = {
            "0": "定时地温",
            "A": "逐日地温",
            "B": "月地温"
        }
        
        # 风向编码映射（16方位）
        self.wind_direction_16 = WIND_DIRECTIONS_16
        
        # 风向编码映射（8方位）
        self.wind_direction_8 = WIND_DIRECTIONS_8
        
        # 地温深度映射（cm）
        self.ground_depths = {
            "0": 0,      # 地表
            "5": 5,      # 5cm
            "10": 10,    # 10cm
            "15": 15,    # 15cm
            "20": 20,    # 20cm
            "40": 40,    # 40cm
            "80": 80,    # 80cm
            "160": 160,  # 160cm
            "320": 320   # 320cm
        }
    
    def _convert_wind_direction(self, direction_str: str, encoding_type: str = "16") -> Optional[Dict[str, Any]]:
        """
        转换风向编码
        
        Args:
            direction_str: 风向字符串
            encoding_type: 编码类型（"16"或"8"）
            
        Returns:
            风向信息字典或None
        """
        if not direction_str or direction_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        direction_str = direction_str.strip()
        
        try:
            # 静风特殊编码
            if direction_str in ["C", "CALM", "00", "静风"]:
                return {
                    "direction": "静风",
                    "angle": 0,
                    "encoding_type": "calm"
                }
            
            # 变风
            if direction_str in ["VRB", "VAR", "变风"]:
                return {
                    "direction": "变风",
                    "encoding_type": "variable"
                }
                
            # 16方位编码
            if encoding_type == "16" and direction_str in self.wind_direction_16:
                return {
                    "direction": self.wind_direction_16[direction_str],
                    "code": direction_str,
                    "encoding_type": "16_point"
                }
            
            # 8方位编码
            if encoding_type == "8" and direction_str in self.wind_direction_8:
                return {
                    "direction": self.wind_direction_8[direction_str],
                    "code": direction_str,
                    "encoding_type": "8_point"
                }
                
            # 数字编码（角度）
            if direction_str.isdigit():
                angle = int(direction_str)
                if 0 <= angle <= 360:
                    return {
                        "angle": angle,
                        "direction": self._angle_to_direction(angle),
                        "encoding_type": "angle"
                    }
                
        except (ValueError, KeyError):
            pass
            
        return None
    
    def _angle_to_direction(self, angle: int) -> str:
        """将角度转换为方向描述"""
        directions = [
            "北", "北北东", "东北", "东北东", "东", "东南东", 
            "东南", "南南东", "南", "南南西", "西南", "西南西",
            "西", "西北西", "西北", "北北西"
        ]
        
        if angle == 0 or angle == 360:
            return "北"
        
        # 每22.5度一个方向
        index = round(angle / 22.5) % 16
        return directions[index]
    
    def _convert_wind_speed(self, speed_str: str) -> Optional[float]:
        """
        转换风速值
        
        Args:
            speed_str: 风速字符串
            
        Returns:
            风速值（m/s）或None
        """
        if not speed_str or speed_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        speed_str = speed_str.strip()
        
        try:
            # 0.1m/s精度格式（如025表示2.5m/s）
            if len(speed_str) == 3 and speed_str.isdigit():
                return float(speed_str) / 10.0
            
            # 直接数值（m/s）
            if speed_str.isdigit():
                return float(speed_str)
            
            # 包含小数点
            if '.' in speed_str:
                return float(speed_str)
                
        except ValueError:
            pass
            
        return None
    
    def _parse_wind_time(self, time_str: str) -> Optional[time]:
        """
        解析风要素中的时间信息
        
        Args:
            time_str: 时间字符串（如HHMM格式）
            
        Returns:
            时间对象或None
        """
        if not time_str or time_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        time_str = time_str.strip()
        
        try:
            if len(time_str) == 4 and time_str.isdigit():
                hour = int(time_str[:2])
                minute = int(time_str[2:])
                
                if 0 <= hour <= 23 and 0 <= minute <= 59:
                    return time(hour, minute)
                    
        except ValueError:
            pass
            
        return None
    
    def _convert_ground_temperature(self, temp_str: str) -> Optional[float]:
        """
        转换地温值
        
        Args:
            temp_str: 地温字符串
            
        Returns:
            地温值（°C）或None
        """
        if not temp_str or temp_str.strip() in MISSING_VALUE_INDICATORS:
            return None
            
        temp_str = temp_str.strip()
        
        try:
            # 负温度（如-042表示-4.2°C）
            if temp_str.startswith('-') and temp_str[1:].isdigit():
                value = float(temp_str[1:])
                if len(temp_str[1:]) == 3:  # 0.1°C精度
                    value = value / 10.0
                return -value
            
            # 正温度，无符号（如+042）
            if temp_str.startswith('+'):
                value_str = temp_str[1:]
                if value_str.isdigit():
                    if len(value_str) == 3:
                        return float(value_str) / 10.0
                    return float(value_str)
            
            # 正温度，0.1°C精度（如042表示4.2°C）
            if len(temp_str) == 3 and temp_str.isdigit():
                return float(temp_str) / 10.0
            
            # 直接整数（°C）
            if temp_str.isdigit():
                return float(temp_str)
                    
        except ValueError:
            pass
            
        return None
    
    def _parse_wind_data_line(self, line: str, mode: str) -> Dict[str, Any]:
        """
        解析风数据行
        
        Args:
            line: 数据行
            mode: 方式位
            
        Returns:
            解析结果字典
        """
        values = line.strip().split()
        result = {
            "raw_line": line,
            "mode": mode,
            "mode_description": self.wind_modes.get(mode, f"方式{mode}"),
            "wind_data": [],
            "valid_count": 0,
            "missing_count": 0
        }
        
        # 根据方式位解析不同格式
        if mode in ["N", "1"]:  # 平均风
            # 格式：风向 风速 风向 风速 ...
            for i in range(0, len(values), 2):
                if i + 1 < len(values):
                    direction_info = self._convert_wind_direction(values[i])
                    speed = self._convert_wind_speed(values[i + 1])
                    
                    wind_point = {
                        "direction_info": direction_info,
                        "speed": speed,
                        "observation_time": None
                    }
                    
                    result["wind_data"].append(wind_point)
                    
                    if direction_info and speed is not None:
                        result["valid_count"] += 1
                    else:
                        result["missing_count"] += 1
        
        elif mode == "2":  # 极大风及时间
            # 格式：风向 风速 时间 风向 风速 时间 ...
            for i in range(0, len(values), 3):
                if i + 2 < len(values):
                    direction_info = self._convert_wind_direction(values[i])
                    speed = self._convert_wind_speed(values[i + 1])
                    wind_time = self._parse_wind_time(values[i + 2])
                    
                    wind_point = {
                        "direction_info": direction_info,
                        "speed": speed,
                        "observation_time": wind_time,
                        "type": "极大风"
                    }
                    
                    result["wind_data"].append(wind_point)
                    
                    if direction_info and speed is not None:
                        result["valid_count"] += 1
                    else:
                        result["missing_count"] += 1
        
        else:  # 其他方式位按默认处理
            for value in values:
                if "=" in value:  # 连接符号
                    result["has_connection"] = True
                    continue
                    
                # 尝试按风向风速解析
                direction_info = self._convert_wind_direction(value)
                speed = self._convert_wind_speed(value)
                
                if direction_info or speed is not None:
                    wind_point = {
                        "direction_info": direction_info,
                        "speed": speed,
                        "observation_time": None
                    }
                    result["wind_data"].append(wind_point)
                    
                    if direction_info or speed is not None:
                        result["valid_count"] += 1
                    else:
                        result["missing_count"] += 1
        
        return result
    
    def _parse_ground_temperature_line(self, line: str, mode: str) -> Dict[str, Any]:
        """
        解析地温数据行
        
        Args:
            line: 数据行
            mode: 方式位
            
        Returns:
            解析结果字典
        """
        values = line.strip().split()
        result = {
            "raw_line": line,
            "mode": mode,
            "mode_description": self.temperature_modes.get(mode, f"方式{mode}"),
            "temperature_data": [],
            "valid_count": 0,
            "missing_count": 0
        }
        
        # 解析地温数据（深度:温度格式）
        for value in values:
            if "=" in value:  # 连接符号
                result["has_connection"] = True
                continue
            
            # 尝试解析深度:温度格式
            if ":" in value:
                parts = value.split(":")
                if len(parts) == 2:
                    depth_str, temp_str = parts
                    depth = self.ground_depths.get(depth_str.strip())
                    temperature = self._convert_ground_temperature(temp_str.strip())
                    
                    temp_point = {
                        "depth": depth,
                        "depth_cm": depth,
                        "temperature": temperature,
                        "raw_value": value
                    }
                    
                    result["temperature_data"].append(temp_point)
                    
                    if depth is not None and temperature is not None:
                        result["valid_count"] += 1
                    else:
                        result["missing_count"] += 1
            else:
                # 直接温度值（默认为地表）
                temperature = self._convert_ground_temperature(value)
                if temperature is not None:
                    temp_point = {
                        "depth": 0,
                        "depth_cm": 0,
                        "temperature": temperature,
                        "raw_value": value
                    }
                    
                    result["temperature_data"].append(temp_point)
                    result["valid_count"] += 1
                else:
                    result["missing_count"] += 1
        
        return result
    
    def parse(self, lines: List[str], element_code: str, mode: str, context: ParseContext) -> ElementData:
        """
        解析风要素或地温数据
        
        Args:
            lines: 数据行列表
            element_code: 要素代码（F/D/K）
            mode: 方式位
            context: 解析上下文
            
        Returns:
            解析结果
        """
        self.logger.debug(f"开始解析{element_code}数据，方式位: {mode}, 行数: {len(lines)}")
        
        # 验证要素代码
        if element_code not in self.supported_elements:
            raise DataValidationError(f"不支持的风温要素代码: {element_code}")
        
        element_name = self.supported_elements[element_code]
        
        # 验证方式位
        if element_code == "F":
            if mode not in self.wind_modes:
                raise DataValidationError(f"要素{element_code}不支持方式位: {mode}")
        else:  # D, K
            if mode not in self.temperature_modes:
                raise DataValidationError(f"要素{element_code}不支持方式位: {mode}")
        
        result = ElementData(
            element_code=element_code,
            element_name=element_name,
            mode=mode,
            segments=[],
            quality_codes=None,
            errors=[],
            warnings=[]
        )
        
        # 统计信息
        total_values = 0
        valid_values = 0
        data_segments = []
        
        # 解析每行数据
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # 跳过空行和分隔行
            if not line or line in ["=", "==", "==="] or line.isspace():
                continue
            
            try:
                if element_code == "F":  # 风要素
                    segment = self._parse_wind_data_line(line, mode)
                else:  # 地温要素
                    segment = self._parse_ground_temperature_line(line, mode)
                
                segment["line_number"] = line_num
                data_segments.append(segment)
                
                total_values += segment["valid_count"] + segment["missing_count"]
                valid_values += segment["valid_count"]
                
            except Exception as e:
                error_msg = f"第{line_num}行解析错误: {str(e)}"
                result.errors.append(error_msg)
                self.logger.error(error_msg)
        
        # 添加数据段
        result.segments.extend(data_segments)
        
        # 数据完整性检查
        if total_values > 0:
            completeness = (valid_values / total_values) * 100
            if completeness < 80:
                warning = f"{element_name}数据完整性较低: {completeness:.1f}%"
                result.warnings.append(warning)
        
        # 特殊验证
        if element_code == "F":
            self._validate_wind_data(result)
        else:
            self._validate_temperature_data(result)
        
        # 生成统计信息段
        if data_segments:
            summary_segment = {
                "summary": True,
                "total_values": total_values,
                "valid_values": valid_values,
                "missing_values": total_values - valid_values,
                "completeness_percent": (valid_values / total_values * 100) if total_values > 0 else 0,
                "element_info": {
                    "code": element_code,
                    "name": element_name,
                    "mode": mode,
                    "mode_description": self.wind_modes.get(mode) if element_code == "F" else self.temperature_modes.get(mode)
                }
            }
            result.segments.append(summary_segment)
        
        if not data_segments:
            result.warnings.append(f"未找到有效的{element_name}数据段")
        
        self.logger.info(f"{element_name}数据解析完成: 方式位{mode}, 有效段数{len(data_segments)}, 错误数{len(result.errors)}")
        
        return result
    
    def _validate_wind_data(self, result: ElementData) -> None:
        """验证风数据"""
        for segment in result.segments:
            if "wind_data" in segment:
                for wind_point in segment["wind_data"]:
                    speed = wind_point.get("speed")
                    if speed is not None:
                        # 异常大风检查
                        if speed > 60:  # 大于60m/s
                            result.warnings.append(f"检测到异常大风速: {speed}m/s")
                        
                        # 静风检查
                        direction_info = wind_point.get("direction_info")
                        if speed == 0 and direction_info and direction_info.get("direction") != "静风":
                            result.warnings.append("风速为0但风向不是静风")
    
    def _validate_temperature_data(self, result: ElementData) -> None:
        """验证地温数据"""
        for segment in result.segments:
            if "temperature_data" in segment:
                for temp_point in segment["temperature_data"]:
                    temperature = temp_point.get("temperature")
                    depth = temp_point.get("depth")
                    
                    if temperature is not None:
                        # 异常温度检查
                        if temperature < -50 or temperature > 60:
                            result.warnings.append(f"检测到异常地温: {temperature}°C (深度{depth}cm)")
                        
                        # 深度相关温度合理性检查
                        if depth is not None and depth > 0:
                            # 深层地温变化应该相对稳定
                            if depth >= 80 and abs(temperature) > 40:
                                result.warnings.append(f"深层地温({depth}cm)数值异常: {temperature}°C") 