"""
降水和天气现象解析器模块

解析A文件中的降水量和天气现象数据，包括定时降水、连接值、现象编码等。
"""

import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from loguru import logger

from ..models import ElementData, ParseContext
from ..exceptions import DataValidationError, FileFormatError
from ..constants import MISSING_VALUE_INDICATORS, WEATHER_PHENOMENA


class PrecipitationWeatherParser:
    """降水和天气现象解析器
    
    支持的要素：
    - R: 降水量（定时降水、连接值、逐小时降水，单位0.1mm）
    - W: 天气现象（编码、时间、强度、移动方向）
    - L: 雷电活动（雷暴、闪电等）
    - Z: 日照时数（实照时数、可照时数）
    
    特殊处理：
    - 降水量支持微量、痕迹降水标记
    - 天气现象编码解析和时间提取
    - 连接值处理和累积降水计算
    - 雷暴等特殊天气现象处理
    """
    
    def __init__(self):
        """初始化解析器"""
        self.logger = logger.bind(parser="PrecipitationWeatherParser")
        
        # 支持的要素映射
        self.element_mapping = {
            'R': {
                'name': '降水量',
                'unit': 'mm',
                'scale': 0.1,
                'range': (0, 9999),
                'modes': ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C']
            },
            'W': {
                'name': '天气现象',
                'unit': '',
                'scale': 1.0,
                'range': None,
                'modes': ['0', '9', 'A']
            },
            'L': {
                'name': '雷电活动',
                'unit': '次',
                'scale': 1.0,
                'range': (0, 999),
                'modes': ['0', '9', 'A']
            },
            'Z': {
                'name': '日照时数',
                'unit': 'h',
                'scale': 0.1,
                'range': (0, 240),
                'modes': ['0', '9', 'A']
            }
        }
        
        # 天气现象编码映射
        self.weather_codes = WEATHER_PHENOMENA
        
        # 特殊降水标记
        self.special_precipitation = {
            'T': 0.0,  # 痕迹（trace）
            'TR': 0.0, # 痕迹
            '99': None, # 缺测
            '9999': None  # 缺测
        }
    
    def parse(self, lines: List[str], element_code: str, mode: str, context: ParseContext) -> ElementData:
        """
        解析降水和天气现象数据
        
        Args:
            lines: 数据行列表（不包括要素标识行）
            element_code: 要素代码（R, W, L, Z）
            mode: 方式位（0-9, A, B, C）
            context: 解析上下文
            
        Returns:
            ElementData对象
            
        Raises:
            FileFormatError: 格式错误
            DataValidationError: 数据验证错误
        """
        self.logger.debug(f"开始解析{element_code}数据，方式位: {mode}, 行数: {len(lines)}")
        
        # 验证要素代码
        if element_code not in self.element_mapping:
            raise DataValidationError(
                f"不支持的降水天气要素代码: {element_code}",
                element_code=element_code,
                value=element_code
            )
        
        element_info = self.element_mapping[element_code]
        
        # 验证方式位
        if mode not in element_info['modes']:
            raise DataValidationError(
                f"要素{element_code}不支持方式位: {mode}",
                element_code=element_code,
                value=mode
            )
        
        element_data = ElementData(
            element_code=element_code,
            element_name=element_info['name'],
            mode=mode,
            segments=[]
        )
        
        # 解析每一行数据
        for i, line in enumerate(lines):
            try:
                segment = self._parse_data_line(line.strip(), i + 1, element_code, mode)
                if segment:
                    element_data.segments.append(segment)
                    
            except Exception as e:
                error_msg = f"解析第{i+1}行{element_info['name']}数据失败: {e}"
                element_data.errors.append(error_msg)
                self.logger.warning(error_msg)
        
        # 验证解析结果
        self._validate_parsed_data(element_data, element_code, mode)
        
        self.logger.info(
            f"{element_info['name']}数据解析完成: 方式位{mode}, "
            f"有效段数{len(element_data.segments)}, "
            f"错误数{len(element_data.errors)}"
        )
        
        return element_data
    
    def _parse_data_line(self, line: str, line_number: int, element_code: str, mode: str) -> Optional[Dict[str, Any]]:
        """
        解析单行降水或天气现象数据
        
        Args:
            line: 数据行内容
            line_number: 行号
            element_code: 要素代码
            mode: 方式位
            
        Returns:
            解析后的数据段字典，如果行为空或无效则返回None
        """
        if not line or line.isspace() or line == '=':
            return None
        
        # 根据要素类型解析数据
        if element_code == 'R':
            return self._parse_precipitation(line, line_number, mode)
        elif element_code == 'W':
            return self._parse_weather_phenomena(line, line_number, mode)
        elif element_code == 'L':
            return self._parse_lightning(line, line_number, mode)
        elif element_code == 'Z':
            return self._parse_sunshine(line, line_number, mode)
        
        return None
    
    def _parse_precipitation(self, line: str, line_number: int, mode: str) -> Dict[str, Any]:
        """解析降水量数据"""
        parts = line.split()
        precipitation_values = []
        total_precipitation = 0.0
        
        for part in parts:
            if part.endswith('.'):
                part = part.rstrip('.')
            
            precip_value = self._convert_precipitation_value(part)
            precipitation_values.append(precip_value)
            
            if precip_value is not None:
                total_precipitation += precip_value
        
        # 检查是否有连接值（累积降水）
        connection_info = self._parse_connection_value(line)
        
        return {
            'line_number': line_number,
            'raw_line': line,
            'precipitation_values': precipitation_values,
            'total_precipitation': total_precipitation,
            'connection_info': connection_info,
            'valid_count': len([v for v in precipitation_values if v is not None]),
            'missing_count': len([v for v in precipitation_values if v is None]),
            'mode_info': {
                'mode': mode,
                'description': self._get_precipitation_mode_description(mode)
            }
        }
    
    def _parse_weather_phenomena(self, line: str, line_number: int, mode: str) -> Dict[str, Any]:
        """解析天气现象数据"""
        phenomena = []
        
        # 天气现象可能有多种格式：编码、时间、强度等
        # 例如：//// 06/11/2023 00000=（第一行是日期信息）
        
        if re.match(r'\d{2}/\d{2}/\d{4}', line):
            # 日期行
            date_match = re.search(r'(\d{2})/(\d{2})/(\d{4})', line)
            if date_match:
                day, month, year = date_match.groups()
                return {
                    'line_number': line_number,
                    'raw_line': line,
                    'date_info': {
                        'day': int(day),
                        'month': int(month),
                        'year': int(year)
                    },
                    'is_date_line': True
                }
        
        # 解析天气现象编码
        parts = line.split()
        for part in parts:
            if part.endswith('.'):
                part = part.rstrip('.')
            
            weather_info = self._parse_weather_code(part)
            if weather_info:
                phenomena.append(weather_info)
        
        return {
            'line_number': line_number,
            'raw_line': line,
            'weather_phenomena': phenomena,
            'valid_count': len(phenomena),
            'mode_info': {
                'mode': mode,
                'description': f"天气现象{mode}方式"
            }
        }
    
    def _parse_lightning(self, line: str, line_number: int, mode: str) -> Dict[str, Any]:
        """解析雷电活动数据"""
        parts = line.split()
        lightning_data = []
        
        for part in parts:
            if part.endswith('.'):
                part = part.rstrip('.')
            
            lightning_value = self._convert_lightning_value(part)
            lightning_data.append(lightning_value)
        
        return {
            'line_number': line_number,
            'raw_line': line,
            'lightning_data': lightning_data,
            'valid_count': len([v for v in lightning_data if v is not None]),
            'missing_count': len([v for v in lightning_data if v is None])
        }
    
    def _parse_sunshine(self, line: str, line_number: int, mode: str) -> Dict[str, Any]:
        """解析日照时数数据"""
        parts = line.split()
        sunshine_values = []
        
        for part in parts:
            if part.endswith('.'):
                part = part.rstrip('.')
            
            sunshine_value = self._convert_sunshine_value(part)
            sunshine_values.append(sunshine_value)
        
        return {
            'line_number': line_number,
            'raw_line': line,
            'sunshine_values': sunshine_values,
            'valid_count': len([v for v in sunshine_values if v is not None]),
            'missing_count': len([v for v in sunshine_values if v is None])
        }
    
    def _convert_precipitation_value(self, value_str: str) -> Optional[float]:
        """
        转换降水量值字符串为数值
        
        Args:
            value_str: 降水量字符串
            
        Returns:
            转换后的降水量值（mm），缺测时返回None
        """
        if not value_str or value_str.strip() in MISSING_VALUE_INDICATORS:
            return None
        
        clean_value = value_str.strip()
        
        # 检查特殊标记
        if clean_value in self.special_precipitation:
            return self.special_precipitation[clean_value]
        
        # 检查缺测标记
        if clean_value in ['0000', '////']:
            return 0.0  # 无降水
        
        try:
            # 转换数值（0.1mm单位）
            raw_value = int(clean_value)
            precip_value = raw_value * 0.1
            
            # 验证合理性（0-999.9mm）
            if 0 <= precip_value <= 999.9:
                return precip_value
            else:
                return None
                
        except (ValueError, TypeError):
            return None
    
    def _parse_connection_value(self, line: str) -> Optional[Dict[str, Any]]:
        """解析连接值信息（累积降水）"""
        # 连接值通常在行末，格式特殊
        # 这里简化处理，实际可能需要更复杂的解析
        if '=' in line:
            return {'has_connection': True, 'raw_connection': line.split('=')[-1]}
        return None
    
    def _parse_weather_code(self, code_str: str) -> Optional[Dict[str, Any]]:
        """解析天气现象编码"""
        if not code_str or code_str in MISSING_VALUE_INDICATORS:
            return None
        
        # 简化的天气现象解析
        # 实际格式可能包含：PPC001, 089022（风向风速）等
        
        if code_str.startswith('PPC'):
            # 降水现象编码
            intensity_code = code_str[3:]
            return {
                'type': 'precipitation',
                'code': code_str,
                'intensity': intensity_code,
                'description': f"降水强度{intensity_code}"
            }
        
        # 风向风速格式：DDDFF（方向3位+风速2位）
        if len(code_str) == 5 and code_str.isdigit():
            direction = int(code_str[:3])
            speed = int(code_str[3:])
            return {
                'type': 'wind',
                'direction': direction,
                'speed': speed,
                'description': f"风向{direction}°风速{speed}m/s"
            }
        
        # 其他天气现象
        weather_desc = self.weather_codes.get(code_str, code_str)
        return {
            'type': 'weather',
            'code': code_str,
            'description': weather_desc
        }
    
    def _convert_lightning_value(self, value_str: str) -> Optional[int]:
        """转换雷电活动数值"""
        if not value_str or value_str.strip() in MISSING_VALUE_INDICATORS:
            return None
        
        try:
            return int(value_str.strip())
        except (ValueError, TypeError):
            return None
    
    def _convert_sunshine_value(self, value_str: str) -> Optional[float]:
        """转换日照时数数值"""
        if not value_str or value_str.strip() in MISSING_VALUE_INDICATORS:
            return None
        
        try:
            raw_value = int(value_str.strip())
            return raw_value * 0.1  # 0.1小时单位
        except (ValueError, TypeError):
            return None
    
    def _get_precipitation_mode_description(self, mode: str) -> str:
        """获取降水方式位描述"""
        mode_descriptions = {
            '0': '4次定时降水',
            '1': '1小时降水',
            '2': '2小时降水', 
            '3': '3小时降水',
            '4': '6小时降水',
            '5': '12小时降水',
            '6': '24小时降水',
            '7': '逐时降水',
            '8': '逐分钟降水',
            '9': '逐3小时降水',
            'A': '逐日降水',
            'B': '连接值降水',
            'C': '特殊时段降水'
        }
        return mode_descriptions.get(mode, f"方式{mode}")
    
    def _validate_parsed_data(self, element_data: ElementData, element_code: str, mode: str) -> None:
        """
        验证解析后的降水天气数据
        
        Args:
            element_data: 解析后的要素数据
            element_code: 要素代码
            mode: 方式位
        """
        element_info = self.element_mapping[element_code]
        
        if not element_data.segments:
            element_data.warnings.append(f"未找到有效的{element_info['name']}数据段")
            return
        
        total_values = 0
        valid_values = 0
        
        # 根据要素类型进行特殊验证
        if element_code == 'R':
            self._validate_precipitation_data(element_data)
        elif element_code == 'W':
            self._validate_weather_data(element_data)
        
        # 通用数据完整性检查
        for segment in element_data.segments:
            if element_code == 'R':
                values = segment.get('precipitation_values', [])
            elif element_code == 'W':
                values = segment.get('weather_phenomena', [])
            elif element_code == 'L':
                values = segment.get('lightning_data', [])
            elif element_code == 'Z':
                values = segment.get('sunshine_values', [])
            else:
                values = []
            
            segment_values = len(values)
            segment_valid = len([v for v in values if v is not None])
            
            total_values += segment_values
            valid_values += segment_valid
        
        # 计算数据完整性
        if total_values > 0:
            completeness = (valid_values / total_values) * 100
            
            if completeness < 50:
                element_data.warnings.append(
                    f"{element_info['name']}数据完整性较低: {completeness:.1f}%"
                )
            
            # 添加统计信息
            element_data.segments.append({
                'line_number': 0,
                'summary': True,
                'total_values': total_values,
                'valid_values': valid_values,
                'completeness_percent': completeness,
                'element_info': {
                    'code': element_code,
                    'name': element_info['name'],
                    'mode': mode,
                    'unit': element_info['unit']
                }
            })
    
    def _validate_precipitation_data(self, element_data: ElementData) -> None:
        """验证降水数据特殊情况"""
        total_precipitation = 0.0
        max_hourly = 0.0
        
        for segment in element_data.segments:
            if 'total_precipitation' in segment:
                total_precipitation += segment['total_precipitation']
            
            # 检查极端降水
            for value in segment.get('precipitation_values', []):
                if value is not None:
                    max_hourly = max(max_hourly, value)
                    
                    # 检查异常大降水
                    if value > 100:  # 超过100mm/小时
                        element_data.warnings.append(
                            f"第{segment['line_number']}行存在异常大降水: {value}mm"
                        )
        
        # 检查总降水合理性
        if total_precipitation > 500:  # 日降水超过500mm
            element_data.warnings.append(
                f"总降水量异常: {total_precipitation:.1f}mm"
            )
    
    def _validate_weather_data(self, element_data: ElementData) -> None:
        """验证天气现象数据特殊情况"""
        weather_types = set()
        
        for segment in element_data.segments:
            for phenomenon in segment.get('weather_phenomena', []):
                if phenomenon and 'type' in phenomenon:
                    weather_types.add(phenomenon['type'])
        
        # 记录识别的天气现象类型
        if weather_types:
            element_data.segments.append({
                'line_number': 0,
                'weather_summary': True,
                'identified_types': list(weather_types),
                'type_count': len(weather_types)
            })


__all__ = ["PrecipitationWeatherParser"] 