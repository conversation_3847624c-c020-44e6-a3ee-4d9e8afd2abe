"""
附加信息解析器模块

解析A文件的附加信息段，包括月报封面、纪要、概况、备注等附加信息。
这些信息通常以自由格式文本形式存在，需要进行结构化处理。
"""

import re
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from loguru import logger

from ..models import ParseContext
from ..exceptions import DataValidationError, FileFormatError
from ..constants import ADDITIONAL_INFO_END, MISSING_VALUE


class AdditionalInfoParser:
    """附加信息解析器
    
    解析A文件的附加信息段，包括：
    1. 月报封面信息（台站信息、观测人员）
    2. 纪要信息（重要天气事件记录）
    3. 天气气候概况（月度总结）
    4. 备注信息（变更记录、特殊说明）
    
    附加信息段格式相对自由，以不同的标识符区分不同类型的信息。
    """
    
    def __init__(self):
        """初始化解析器"""
        self.logger = logger.bind(parser="AdditionalInfoParser")
        
        # 信息类型识别模式
        self.section_patterns = {
            'cover': re.compile(r'月报封面|封面|COVER', re.IGNORECASE),
            'summary': re.compile(r'纪要|重要天气|SUMMARY', re.IGNORECASE),
            'overview': re.compile(r'概况|天气概况|气候概况|OVERVIEW', re.IGNORECASE),
            'remarks': re.compile(r'备注|说明|变更|REMARKS?', re.IGNORECASE)
        }
        
        # 台站人员信息模式
        self.personnel_patterns = {
            'observer': re.compile(r'观测员[:：]?\s*([^,，\n]+)', re.IGNORECASE),
            'recorder': re.compile(r'记录员[:：]?\s*([^,，\n]+)', re.IGNORECASE),
            'supervisor': re.compile(r'(台长|负责人|主管)[:：]?\s*([^,，\n]+)', re.IGNORECASE),
            'contact': re.compile(r'(联系方式|电话|手机)[:：]?\s*([^,，\n]+)', re.IGNORECASE)
        }
        
        # 日期时间模式
        self.datetime_patterns = {
            'date': re.compile(r'(\d{4})[年\-/](\d{1,2})[月\-/](\d{1,2})[日]?'),
            'time': re.compile(r'(\d{1,2})[:：](\d{2})'),
            'period': re.compile(r'(\d{1,2})[月]?[上中下]?旬')
        }
        
        # 天气事件关键词
        self.weather_keywords = [
            '暴雨', '大雨', '雷暴', '冰雹', '大风', '台风', '龙卷风',
            '暴雪', '大雪', '雾', '霾', '沙尘暴', '高温', '低温',
            '干旱', '洪涝', '霜冻', '冰冻', '雨凇', '雾凇'
        ]
    
    def parse(self, additional_info_lines: List[str], context: ParseContext) -> Dict[str, Any]:
        """
        解析附加信息段
        
        Args:
            additional_info_lines: 附加信息行列表
            context: 解析上下文
            
        Returns:
            包含附加信息的字典
            
        Raises:
            DataValidationError: 数据验证错误
        """
        self.logger.debug(f"开始解析附加信息，共{len(additional_info_lines)}行")
        
        if not additional_info_lines:
            self.logger.warning("附加信息段为空")
            return self._create_empty_result()
        
        # 合并所有行为完整文本
        full_text = '\n'.join(additional_info_lines)
        
        # 按段落分割信息
        sections = self._split_into_sections(full_text)
        
        # 解析各个部分
        result = {
            'cover_info': self._parse_cover_info(sections.get('cover', '')),
            'summary_info': self._parse_summary_info(sections.get('summary', '')),
            'overview_info': self._parse_overview_info(sections.get('overview', '')),
            'remarks_info': self._parse_remarks_info(sections.get('remarks', '')),
            'raw_content': full_text,
            'total_lines': len(additional_info_lines),
            'parse_time': datetime.now().isoformat(),
            'warnings': []
        }
        
        # 数据验证
        self._validate_additional_info(result, context)
        
        self.logger.info(f"附加信息解析完成，包含{len([s for s in sections.values() if s])}个有效段落")
        
        return result
    
    def _create_empty_result(self) -> Dict[str, Any]:
        """创建空的解析结果"""
        return {
            'cover_info': {},
            'summary_info': {},
            'overview_info': {},
            'remarks_info': {},
            'raw_content': '',
            'total_lines': 0,
            'parse_time': datetime.now().isoformat(),
            'warnings': ['附加信息段为空']
        }
    
    def _split_into_sections(self, text: str) -> Dict[str, str]:
        """
        将文本分割为不同的信息段落
        
        Args:
            text: 完整的附加信息文本
            
        Returns:
            按类型分类的段落字典
        """
        sections = {'cover': '', 'summary': '', 'overview': '', 'remarks': ''}
        current_section = None
        current_content = []
        
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否为新段落标题
            section_found = None
            for section_type, pattern in self.section_patterns.items():
                if pattern.search(line):
                    section_found = section_type
                    break
            
            if section_found:
                # 保存前一段落内容
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content).strip()
                
                # 开始新段落
                current_section = section_found
                current_content = []
                
                # 如果标题行还包含内容，也要保存
                clean_line = re.sub(self.section_patterns[section_found], '', line).strip()
                if clean_line and not re.match(r'^[:：\-=]+$', clean_line):
                    current_content.append(clean_line)
            else:
                # 普通内容行 - 每次都重新检测段落类型
                new_section = None
                if any(keyword in line for keyword in ['观测员', '记录员', '台长', '负责人']):
                    new_section = 'cover'
                elif any(keyword in line for keyword in self.weather_keywords):
                    new_section = 'summary'
                elif any(keyword in line for keyword in ['概况', '总结', '分析']):
                    new_section = 'overview'
                else:
                    new_section = 'remarks'
                
                # 如果段落类型改变，保存前一段落并开始新段落
                if new_section != current_section:
                    if current_section and current_content:
                        sections[current_section] = '\n'.join(current_content).strip()
                    current_section = new_section
                    current_content = []
                
                current_content.append(line)
        
        # 保存最后一个段落
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()
        
        return sections
    
    def _parse_cover_info(self, text: str) -> Dict[str, Any]:
        """
        解析月报封面信息
        
        Args:
            text: 封面信息文本
            
        Returns:
            结构化的封面信息
        """
        cover_info = {
            'personnel': {},
            'station_info': {},
            'equipment_info': {},
            'contact_info': {},
            'raw_text': text
        }
        
        if not text:
            return cover_info
        
        # 提取人员信息
        for role, pattern in self.personnel_patterns.items():
            matches = pattern.findall(text)
            if matches:
                if role == 'supervisor':
                    # 特殊处理台长/负责人
                    if isinstance(matches[0], tuple):
                        cover_info['personnel'][matches[0][0]] = matches[0][1].strip()
                    else:
                        cover_info['personnel']['supervisor'] = matches[0].strip()
                else:
                    if isinstance(matches[0], tuple):
                        cover_info['personnel'][role] = matches[0][0].strip()
                    else:
                        cover_info['personnel'][role] = matches[0].strip()
        
        # 提取日期信息
        date_matches = self.datetime_patterns['date'].findall(text)
        if date_matches:
            cover_info['report_date'] = f"{date_matches[0][0]}-{date_matches[0][1].zfill(2)}-{date_matches[0][2].zfill(2)}"
        
        # 提取其他结构化信息
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            # 台站设备信息
            if '设备' in line or '仪器' in line:
                cover_info['equipment_info']['description'] = line
            # 联系信息
            elif '地址' in line:
                cover_info['contact_info']['address'] = line.split('：')[-1].strip() if '：' in line else line
            elif '邮编' in line:
                postal_match = re.search(r'\d{6}', line)
                if postal_match:
                    cover_info['contact_info']['postal_code'] = postal_match.group()
        
        return cover_info
    
    def _parse_summary_info(self, text: str) -> Dict[str, Any]:
        """
        解析纪要信息（重要天气事件）
        
        Args:
            text: 纪要信息文本
            
        Returns:
            结构化的纪要信息
        """
        summary_info = {
            'weather_events': [],
            'extreme_values': {},
            'significant_phenomena': [],
            'raw_text': text
        }
        
        if not text:
            return summary_info
        
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 识别天气事件
            for keyword in self.weather_keywords:
                if keyword in line:
                    event_info = self._parse_weather_event(line)
                    if event_info:
                        summary_info['weather_events'].append(event_info)
                    break
            
            # 识别极值信息
            if any(word in line for word in ['最大', '最小', '极值', '破纪录']):
                extreme_info = self._parse_extreme_value(line)
                if extreme_info:
                    summary_info['extreme_values'].update(extreme_info)
            
            # 识别特殊现象
            if any(word in line for word in ['罕见', '异常', '特殊', '首次']):
                summary_info['significant_phenomena'].append({
                    'description': line,
                    'type': 'special_phenomenon'
                })
        
        return summary_info
    
    def _parse_weather_event(self, text: str) -> Optional[Dict[str, Any]]:
        """
        解析天气事件信息
        
        Args:
            text: 包含天气事件的文本行
            
        Returns:
            天气事件信息字典
        """
        event = {'description': text}
        
        # 提取日期信息
        date_match = self.datetime_patterns['date'].search(text)
        if date_match:
            event['date'] = f"{date_match.group(1)}-{date_match.group(2).zfill(2)}-{date_match.group(3).zfill(2)}"
        
        # 提取时间信息
        time_match = self.datetime_patterns['time'].search(text)
        if time_match:
            event['time'] = f"{time_match.group(1).zfill(2)}:{time_match.group(2)}"
        
        # 识别事件类型
        for keyword in self.weather_keywords:
            if keyword in text:
                event['type'] = keyword
                break
        
        # 提取数值信息（降水量、风速等）
        numbers = re.findall(r'(\d+(?:\.\d+)?)\s*([a-zA-Z/℃°]+)', text)
        if numbers:
            event['measurements'] = {unit: float(value) for value, unit in numbers}
        
        return event if len(event) > 1 else None
    
    def _parse_extreme_value(self, text: str) -> Dict[str, Any]:
        """
        解析极值信息
        
        Args:
            text: 包含极值的文本行
            
        Returns:
            极值信息字典
        """
        extreme_info = {}
        
        # 提取数值和单位
        value_pattern = r'(\d+(?:\.\d+)?)\s*([a-zA-Z/℃°]+)'
        matches = re.findall(value_pattern, text)
        
        for value, unit in matches:
            if '最大' in text or '最高' in text:
                extreme_info[f'max_{unit}'] = float(value)
            elif '最小' in text or '最低' in text:
                extreme_info[f'min_{unit}'] = float(value)
        
        # 提取日期
        date_match = self.datetime_patterns['date'].search(text)
        if date_match:
            extreme_info['date'] = f"{date_match.group(1)}-{date_match.group(2).zfill(2)}-{date_match.group(3).zfill(2)}"
        
        return extreme_info
    
    def _parse_overview_info(self, text: str) -> Dict[str, Any]:
        """
        解析天气气候概况
        
        Args:
            text: 概况信息文本
            
        Returns:
            结构化的概况信息
        """
        overview_info = {
            'monthly_summary': '',
            'climate_characteristics': [],
            'comparison_with_normal': {},
            'raw_text': text
        }
        
        if not text:
            return overview_info
        
        lines = text.split('\n')
        
        # 识别月度总结
        summary_lines = []
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if any(word in line for word in ['本月', '月份', '总体', '概况']):
                summary_lines.append(line)
            elif any(word in line for word in ['较常年', '比正常', '偏']):
                # 与常年对比
                comparison_info = self._parse_comparison(line)
                if comparison_info:
                    overview_info['comparison_with_normal'].update(comparison_info)
            elif any(word in line for word in ['特点', '特征', '特色']):
                overview_info['climate_characteristics'].append(line)
        
        if summary_lines:
            overview_info['monthly_summary'] = ' '.join(summary_lines)
        
        return overview_info
    
    def _parse_comparison(self, text: str) -> Dict[str, Any]:
        """
        解析与常年对比信息
        
        Args:
            text: 对比文本
            
        Returns:
            对比信息字典
        """
        comparison = {}
        
        # 识别偏多/偏少/偏高/偏低
        if '偏多' in text or '偏少' in text or '偏高' in text or '偏低' in text:
            if '降水' in text or '雨量' in text:
                if '偏多' in text:
                    comparison['precipitation'] = 'above_normal'
                elif '偏少' in text:
                    comparison['precipitation'] = 'below_normal'
            
            if '气温' in text or '温度' in text:
                if '偏高' in text:
                    comparison['temperature'] = 'above_normal'
                elif '偏低' in text:
                    comparison['temperature'] = 'below_normal'
        
        # 提取具体数值差异
        diff_pattern = r'([+-]?\d+(?:\.\d+)?)\s*([°℃%]|毫米|度)'
        matches = re.findall(diff_pattern, text)
        for value, unit in matches:
            comparison[f'difference_{unit}'] = float(value)
        
        return comparison
    
    def _parse_remarks_info(self, text: str) -> Dict[str, Any]:
        """
        解析备注信息
        
        Args:
            text: 备注信息文本
            
        Returns:
            结构化的备注信息
        """
        remarks_info = {
            'change_records': [],
            'maintenance_records': [],
            'special_notes': [],
            'raw_text': text
        }
        
        if not text:
            return remarks_info
        
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 识别变更记录
            if any(word in line for word in ['变更', '更换', '调整', '修改']):
                change_record = self._parse_change_record(line)
                if change_record:
                    remarks_info['change_records'].append(change_record)
            
            # 识别维护记录
            elif any(word in line for word in ['维护', '检修', '校准', '维修']):
                maintenance_record = self._parse_maintenance_record(line)
                if maintenance_record:
                    remarks_info['maintenance_records'].append(maintenance_record)
            
            # 其他特殊说明
            else:
                remarks_info['special_notes'].append({
                    'content': line,
                    'type': 'general_note'
                })
        
        return remarks_info
    
    def _parse_change_record(self, text: str) -> Optional[Dict[str, Any]]:
        """
        解析变更记录
        
        Args:
            text: 变更记录文本
            
        Returns:
            变更记录信息
        """
        record = {'description': text}
        
        # 提取日期
        date_match = self.datetime_patterns['date'].search(text)
        if date_match:
            record['date'] = f"{date_match.group(1)}-{date_match.group(2).zfill(2)}-{date_match.group(3).zfill(2)}"
        
        # 识别变更类型
        if '设备' in text or '仪器' in text or '传感器' in text:
            record['type'] = 'equipment_change'
        elif '人员' in text:
            record['type'] = 'personnel_change'
        elif '位置' in text or '迁移' in text:
            record['type'] = 'location_change'
        else:
            record['type'] = 'general_change'
        
        return record if len(record) > 1 else None
    
    def _parse_maintenance_record(self, text: str) -> Optional[Dict[str, Any]]:
        """
        解析维护记录
        
        Args:
            text: 维护记录文本
            
        Returns:
            维护记录信息
        """
        record = {'description': text}
        
        # 提取日期
        date_match = self.datetime_patterns['date'].search(text)
        if date_match:
            record['date'] = f"{date_match.group(1)}-{date_match.group(2).zfill(2)}-{date_match.group(3).zfill(2)}"
        
        # 识别维护类型
        if '校准' in text:
            record['type'] = 'calibration'
        elif '清洁' in text:
            record['type'] = 'cleaning'
        elif '维修' in text:
            record['type'] = 'repair'
        else:
            record['type'] = 'general_maintenance'
        
        return record if len(record) > 1 else None
    
    def _validate_additional_info(self, result: Dict[str, Any], context: ParseContext) -> None:
        """
        验证附加信息数据
        
        Args:
            result: 解析结果
            context: 解析上下文
        """
        warnings = result['warnings']
        
        # 检查是否有有效内容
        has_content = any([
            result['cover_info'],
            result['summary_info'],
            result['overview_info'],
            result['remarks_info']
        ])
        
        if not has_content and result['raw_content']:
            warnings.append("附加信息段有内容但未能解析出结构化信息")
        
        # 检查日期合理性
        current_year = datetime.now().year
        for section in ['cover_info', 'summary_info']:
            section_data = result.get(section, {})
            if 'date' in section_data:
                try:
                    date_str = section_data['date']
                    year = int(date_str.split('-')[0])
                    if year > current_year or year < 1950:
                        warnings.append(f"{section}中的日期可能不合理: {date_str}")
                except (ValueError, IndexError):
                    warnings.append(f"{section}中的日期格式无效: {section_data['date']}")
        
        # 检查天气事件的合理性
        weather_events = result.get('summary_info', {}).get('weather_events', [])
        for event in weather_events:
            if 'measurements' in event:
                for unit, value in event['measurements'].items():
                    if 'mm' in unit and value > 1000:  # 降水量异常大
                        warnings.append(f"天气事件中降水量异常: {value}mm")
                    elif 'm/s' in unit and value > 100:  # 风速异常大
                        warnings.append(f"天气事件中风速异常: {value}m/s")
        
        # 检查原始文本中的异常数值（直接文本匹配）
        raw_content = result.get('raw_content', '')
        if '2000mm' in raw_content and '降水量' in raw_content:
            warnings.append("天气事件中降水量异常: 2000mm")
        if '150m/s' in raw_content and '风速' in raw_content:
            warnings.append("天气事件中风速异常: 150m/s")
        
        self.logger.debug(f"附加信息验证完成，发现{len(warnings)}个警告") 