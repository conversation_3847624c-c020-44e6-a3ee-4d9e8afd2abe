"""
数据模型模块

定义气象数据解析的标准数据结构和结果对象。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple
import uuid

from .constants import QualityCode


class ParseStatus(Enum):
    """解析状态枚举"""
    PENDING = "pending"          # 待解析
    PARSING = "parsing"          # 解析中
    SUCCESS = "success"          # 解析成功
    WARNING = "warning"          # 解析成功但有警告
    ERROR = "error"              # 解析失败
    CANCELLED = "cancelled"      # 解析取消


class DataQuality(Enum):
    """数据质量等级"""
    EXCELLENT = "excellent"      # 优秀
    GOOD = "good"               # 良好
    ACCEPTABLE = "acceptable"    # 可接受
    POOR = "poor"               # 较差
    UNUSABLE = "unusable"       # 不可用


@dataclass
class StationInfo:
    """台站信息数据结构"""
    station_id: str                 # 区站号
    latitude: float                 # 纬度（度）
    longitude: float                # 经度（度）
    latitude_direction: str         # 纬度方向（N/S）
    longitude_direction: str        # 经度方向（E/W）
    observation_altitude: float     # 观测场拔海高度（m）
    pressure_sensor_altitude: float # 气压感应器拔海高度（m）
    wind_sensor_height: float       # 风速感应器距地高度（m）
    platform_height: float         # 观测平台距地高度（m）
    observation_method: int         # 观测方式（0=人工，1=自动）
    station_type: int               # 测站类别
    observation_items: str          # 观测项目标识（20位）
    quality_control_flag: int       # 质量控制指示码
    year: int                       # 年份
    month: int                      # 月份
    
    @property
    def altitude(self) -> float:
        """获取主要高度（观测场高度）"""
        return self.observation_altitude
    
    @property
    def coordinate_string(self) -> str:
        """获取坐标字符串表示"""
        return f"{self.latitude:.4f}°{self.latitude_direction}, {self.longitude:.4f}°{self.longitude_direction}"
    
    @property
    def is_automatic(self) -> bool:
        """是否为自动站"""
        return self.observation_method == 1
    
    def to_decimal_degrees(self) -> tuple[float, float]:
        """转换为十进制度格式"""
        return self.latitude, self.longitude
    
    def to_degrees_minutes(self) -> tuple[str, str]:
        """转换为度分格式"""
        # 纬度
        lat_abs = abs(self.latitude)
        lat_deg = int(lat_abs)
        lat_min = int((lat_abs - lat_deg) * 60)
        lat_dir = 'N' if self.latitude >= 0 else 'S'
        lat_str = f"{lat_deg:02d}{lat_min:02d}{lat_dir}"
        
        # 经度
        lon_abs = abs(self.longitude)
        lon_deg = int(lon_abs)
        lon_min = int((lon_abs - lon_deg) * 60)
        lon_dir = 'E' if self.longitude >= 0 else 'W'
        lon_str = f"{lon_deg:03d}{lon_min:02d}{lon_dir}"
        
        return lat_str, lon_str
    
    def get_coordinate_info(self) -> dict:
        """获取坐标详细信息"""
        return {
            'decimal_degrees': {
                'latitude': self.latitude,
                'longitude': self.longitude
            },
            'degrees_minutes': {
                'latitude': f"{abs(self.latitude):.4f}°{self.latitude_direction}",
                'longitude': f"{abs(self.longitude):.4f}°{self.longitude_direction}"
            },
            'original_format': self.to_degrees_minutes()
        }
    
    def get_height_info(self) -> dict:
        """获取高度信息"""
        return {
            'observation_altitude': self.observation_altitude,
            'pressure_sensor_altitude': self.pressure_sensor_altitude,
            'wind_sensor_height': self.wind_sensor_height,
            'platform_height': self.platform_height,
            'altitude_difference': abs(self.pressure_sensor_altitude - self.observation_altitude)
        }


@dataclass
class ElementData:
    """要素数据结构"""
    element_code: str               # 要素代码（P, T, U等）
    element_name: str               # 要素名称
    mode: str                       # 方式位
    segments: List[Dict[str, Any]]  # 数据段列表
    quality_codes: Optional[List[str]] = None  # 质量控制码
    errors: List[str] = field(default_factory=list)  # 错误信息
    warnings: List[str] = field(default_factory=list)  # 警告信息
    
    @property
    def has_data(self) -> bool:
        """是否包含有效数据"""
        return bool(self.segments)
    
    @property
    def data_quality(self) -> DataQuality:
        """评估数据质量"""
        if self.errors:
            return DataQuality.UNUSABLE
        elif self.warnings:
            return DataQuality.ACCEPTABLE
        elif self.has_data:
            return DataQuality.GOOD
        else:
            return DataQuality.POOR


@dataclass 
class QualityControlInfo:
    """质量控制信息"""
    has_quality_section: bool = False
    quality_codes: Dict[str, List[str]] = field(default_factory=dict)
    corrections: List[Dict[str, Any]] = field(default_factory=list)
    error_summary: Dict[str, int] = field(default_factory=dict)


@dataclass
class AdditionalInfo:
    """附加信息数据结构"""
    monthly_report: Dict[str, str] = field(default_factory=dict)  # 月报封面
    memo: List[Dict[str, Any]] = field(default_factory=list)      # 纪要
    weather_overview: Dict[str, str] = field(default_factory=dict) # 天气概况
    remarks: List[Dict[str, Any]] = field(default_factory=list)   # 备注


@dataclass
class ParseResult:
    """解析结果数据结构"""
    # 基本信息
    result_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    file_path: Optional[Path] = None
    parse_timestamp: datetime = field(default_factory=datetime.now)
    status: ParseStatus = ParseStatus.PENDING
    
    # 解析数据
    station_info: Optional[StationInfo] = None
    elements_data: Dict[str, ElementData] = field(default_factory=dict)
    quality_control: Optional[QualityControlInfo] = None
    additional_info: Optional[AdditionalInfo] = None
    
    # 统计信息
    total_elements: int = 0
    parsed_elements: int = 0
    error_count: int = 0
    warning_count: int = 0
    
    # 错误和警告
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # 性能指标
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    parse_duration: Optional[float] = None
    
    def add_element_data(self, element_data: ElementData) -> None:
        """添加要素数据"""
        self.elements_data[element_data.element_code] = element_data
        self.parsed_elements += 1
        
        # 累计错误和警告
        self.error_count += len(element_data.errors)
        self.warning_count += len(element_data.warnings)
        self.errors.extend(element_data.errors)
        self.warnings.extend(element_data.warnings)
    
    def add_error(self, error_message: str) -> None:
        """添加全局错误"""
        self.errors.append(error_message)
        self.error_count += 1
    
    def add_warning(self, warning_message: str) -> None:
        """添加全局警告"""
        self.warnings.append(warning_message)
        self.warning_count += 1
    
    def start_parsing(self) -> None:
        """开始解析"""
        self.start_time = datetime.now()
        self.status = ParseStatus.PARSING
    
    def finish_parsing(self, success: bool = True) -> None:
        """完成解析"""
        self.end_time = datetime.now()
        if self.start_time:
            self.parse_duration = (self.end_time - self.start_time).total_seconds()
        
        # 根据错误数量设置状态
        if not success or self.error_count > 0:
            self.status = ParseStatus.ERROR
        elif self.warning_count > 0:
            self.status = ParseStatus.WARNING
        else:
            self.status = ParseStatus.SUCCESS
    
    @property
    def success_rate(self) -> float:
        """解析成功率"""
        if self.total_elements == 0:
            return 0.0
        return self.parsed_elements / self.total_elements
    
    @property
    def overall_quality(self) -> DataQuality:
        """整体数据质量评估"""
        if self.error_count > 0:
            return DataQuality.UNUSABLE
        elif self.warning_count > 0:
            return DataQuality.ACCEPTABLE
        elif self.success_rate >= 0.9:
            return DataQuality.EXCELLENT
        elif self.success_rate >= 0.7:
            return DataQuality.GOOD
        else:
            return DataQuality.POOR
    
    def get_summary(self) -> Dict[str, Any]:
        """获取解析结果摘要"""
        return {
            "result_id": self.result_id,
            "file_path": str(self.file_path) if self.file_path else None,
            "status": self.status.value,
            "station_id": self.station_info.station_id if self.station_info else None,
            "total_elements": self.total_elements,
            "parsed_elements": self.parsed_elements,
            "success_rate": self.success_rate,
            "error_count": self.error_count,
            "warning_count": self.warning_count,
            "overall_quality": self.overall_quality.value,
            "parse_duration": self.parse_duration,
            "parse_timestamp": self.parse_timestamp.isoformat()
        }


@dataclass
class ParseContext:
    """解析上下文信息"""
    file_path: Path
    options: Dict[str, Any] = field(default_factory=dict)
    current_line: int = 0
    current_element: Optional[str] = None
    current_segment: int = 0
    strict_mode: bool = True
    timeout_seconds: Optional[float] = None
    
    def update_progress(self, line: int, element: Optional[str] = None) -> None:
        """更新解析进度"""
        self.current_line = line
        if element:
            self.current_element = element


class ProgressCallback(ABC):
    """进度回调接口"""
    
    @abstractmethod
    def on_start(self, context: ParseContext) -> None:
        """开始解析时调用"""
        pass
    
    @abstractmethod
    def on_progress(self, context: ParseContext, progress: float) -> None:
        """解析进度更新时调用"""
        pass
    
    @abstractmethod
    def on_element_parsed(self, context: ParseContext, element_data: ElementData) -> None:
        """要素解析完成时调用"""
        pass
    
    @abstractmethod
    def on_complete(self, context: ParseContext, result: ParseResult) -> None:
        """解析完成时调用"""
        pass
    
    @abstractmethod
    def on_error(self, context: ParseContext, error: Exception) -> None:
        """发生错误时调用"""
        pass


__all__ = [
    "ParseStatus",
    "DataQuality", 
    "StationInfo",
    "ElementData",
    "QualityControlInfo",
    "AdditionalInfo",
    "ParseResult",
    "ParseContext",
    "ProgressCallback"
] 