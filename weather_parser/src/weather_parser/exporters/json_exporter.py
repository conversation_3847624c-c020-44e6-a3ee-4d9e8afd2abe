"""
JSON导出器

实现JSON格式的数据导出，适合API和数据交换
"""

import json
from typing import Any, Dict, List, Union
from pathlib import Path
import logging
from datetime import datetime, date, time

from .base_exporter import BaseExporter, ExportOptions
from ..models import ParseResult, StationInfo, ElementData

logger = logging.getLogger(__name__)


class JSONExporter(BaseExporter):
    """
    JSON导出器
    
    将气象数据导出为JSON格式，保持原始结构和数据类型
    """
    
    def __init__(self):
        super().__init__("JSONExporter")
        self.indent = 2
        self.ensure_ascii = False
        self.sort_keys = True
    
    def export(
        self,
        data: Union[ParseResult, List[ParseResult], Dict[str, Any]],
        options: ExportOptions
    ) -> Dict[str, Any]:
        """
        导出数据为JSON格式
        
        Args:
            data: 要导出的数据
            options: 导出选项
            
        Returns:
            Dict[str, Any]: 导出结果信息
        """
        self.validate_options(options)
        
        if not options.output_path:
            raise ValueError("JSON导出需要指定输出路径")
        
        # 确保输出目录存在
        options.output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 准备数据
        prepared_data = self.prepare_data(data, options)
        
        # 序列化为JSON
        json_data = self._serialize_to_json(prepared_data)
        
        # 写入文件
        with open(options.output_path, 'w', encoding=options.encoding) as f:
            json.dump(
                json_data,
                f,
                indent=self.indent,
                ensure_ascii=self.ensure_ascii,
                sort_keys=self.sort_keys,
                cls=self.WeatherDataJSONEncoder
            )
        
        self._update_statistics()
        
        self.logger.info(f"JSON导出完成: {options.output_path}")
        
        # 计算统计信息
        file_size = options.output_path.stat().st_size
        
        result = {
            'format': 'json',
            'output_path': str(options.output_path),
            'file_size': file_size,
            'encoding': options.encoding
        }
        
        # 添加数据特定统计
        if isinstance(data, ParseResult):
            result.update({
                'observations_count': len(data.elements) if hasattr(data, 'elements') and data.elements else (len(data.observations) if hasattr(data, 'observations') and data.observations else 0),
                'files_processed': 1
            })
        elif isinstance(data, list):
            total_observations = sum(
                len(item.observations) if item.observations else 0 
                for item in data if isinstance(item, ParseResult)
            )
            result.update({
                'observations_count': total_observations,
                'files_processed': len(data)
            })
        
        return result
    
    def get_supported_extensions(self) -> List[str]:
        """获取支持的文件扩展名"""
        return ['.json']
    
    def _serialize_to_json(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        序列化数据为JSON兼容格式
        
        Args:
            data: 要序列化的数据
            
        Returns:
            Dict[str, Any]: JSON兼容的数据
        """
        return self._convert_for_json(data)
    
    def _convert_for_json(self, obj: Any) -> Any:
        """
        转换对象为JSON兼容格式
        
        Args:
            obj: 要转换的对象
            
        Returns:
            Any: JSON兼容的对象
        """
        if isinstance(obj, dict):
            return {key: self._convert_for_json(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_for_json(item) for item in obj]
        elif isinstance(obj, (datetime, date, time)):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            # 转换对象为字典
            return self._convert_for_json(obj.__dict__)
        else:
            return obj
    
    class WeatherDataJSONEncoder(json.JSONEncoder):
        """自定义JSON编码器，处理特殊数据类型"""
        
        def default(self, obj):
            if isinstance(obj, (datetime, date, time)):
                return obj.isoformat()
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
            elif hasattr(obj, '__slots__'):
                return {slot: getattr(obj, slot, None) for slot in obj.__slots__}
            return super().default(obj)
    
    def export_compact(
        self,
        data: Union[ParseResult, List[ParseResult], Dict[str, Any]],
        options: ExportOptions
    ) -> Dict[str, Any]:
        """
        导出紧凑格式的JSON（无缩进）
        
        Args:
            data: 要导出的数据
            options: 导出选项
            
        Returns:
            Dict[str, Any]: 导出结果信息
        """
        self.validate_options(options)
        
        if not options.output_path:
            raise ValueError("JSON导出需要指定输出路径")
        
        # 确保输出目录存在
        options.output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 准备数据
        prepared_data = self.prepare_data(data, options)
        json_data = self._serialize_to_json(prepared_data)
        
        # 写入紧凑格式
        with open(options.output_path, 'w', encoding=options.encoding) as f:
            json.dump(
                json_data,
                f,
                separators=(',', ':'),  # 紧凑分隔符
                ensure_ascii=self.ensure_ascii,
                cls=self.WeatherDataJSONEncoder
            )
        
        self._update_statistics()
        
        file_size = options.output_path.stat().st_size
        
        return {
            'format': 'json',
            'output_path': str(options.output_path),
            'file_size': file_size,
            'encoding': options.encoding,
            'compact': True
        }
    
    def export_pretty(
        self,
        data: Union[ParseResult, List[ParseResult], Dict[str, Any]],
        options: ExportOptions,
        indent: int = 4
    ) -> Dict[str, Any]:
        """
        导出格式化的JSON（带缩进）
        
        Args:
            data: 要导出的数据
            options: 导出选项
            indent: 缩进空格数
            
        Returns:
            Dict[str, Any]: 导出结果信息
        """
        original_indent = self.indent
        self.indent = indent
        
        try:
            result = self.export(data, options)
            result['pretty'] = True
            result['indent'] = indent
            return result
        finally:
            self.indent = original_indent
    
    def export_schema(
        self,
        data: Union[ParseResult, List[ParseResult]],
        options: ExportOptions
    ) -> Dict[str, Any]:
        """
        导出数据结构模式
        
        Args:
            data: 要分析的数据
            options: 导出选项
            
        Returns:
            Dict[str, Any]: 导出结果信息
        """
        if not options.output_path:
            raise ValueError("JSON导出需要指定输出路径")
        
        # 确保输出目录存在
        options.output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 分析数据结构
        schema = self._analyze_data_schema(data)
        
        # 写入模式文件
        with open(options.output_path, 'w', encoding=options.encoding) as f:
            json.dump(
                schema,
                f,
                indent=self.indent,
                ensure_ascii=self.ensure_ascii,
                sort_keys=self.sort_keys
            )
        
        return {
            'format': 'json_schema',
            'output_path': str(options.output_path),
            'schema_version': '1.0',
            'encoding': options.encoding
        }
    
    def _analyze_data_schema(
        self,
        data: Union[ParseResult, List[ParseResult]]
    ) -> Dict[str, Any]:
        """
        分析数据结构生成模式
        
        Args:
            data: 要分析的数据
            
        Returns:
            Dict[str, Any]: 数据模式
        """
        if isinstance(data, ParseResult):
            data = [data]
        
        schema = {
            'schema_version': '1.0',
            'generated_at': datetime.now().isoformat(),
            'description': '气象数据结构模式',
            'data_structure': {}
        }
        
        # 分析第一个有效结果的结构
        for result in data:
            elements = getattr(result, 'elements', None) or getattr(result, 'observations', None)
            if elements:
                # 分析观测数据结构
                obs_schema = self._analyze_object_schema(elements[0])
                schema['data_structure']['observation'] = obs_schema
                
                # 分析台站信息结构
                if result.station_info:
                    station_schema = self._analyze_object_schema(result.station_info)
                    schema['data_structure']['station_info'] = station_schema
                
                break
        
        return schema
    
    def _analyze_object_schema(self, obj: Any) -> Dict[str, Any]:
        """
        分析对象结构
        
        Args:
            obj: 要分析的对象
            
        Returns:
            Dict[str, Any]: 对象模式
        """
        schema = {}
        
        if hasattr(obj, '__dict__'):
            obj_dict = obj.__dict__
        else:
            obj_dict = obj if isinstance(obj, dict) else {}
        
        for key, value in obj_dict.items():
            schema[key] = {
                'type': type(value).__name__,
                'required': value is not None,
                'description': f'{key}字段'
            }
            
            if isinstance(value, list) and value:
                schema[key]['item_type'] = type(value[0]).__name__
            elif isinstance(value, dict):
                schema[key]['properties'] = self._analyze_object_schema(value)
        
        return schema
    
    def export_geojson(
        self,
        data: Union[ParseResult, List[ParseResult]],
        options: ExportOptions
    ) -> Dict[str, Any]:
        """
        导出GeoJSON格式（包含地理位置信息）
        
        Args:
            data: 要导出的数据
            options: 导出选项
            
        Returns:
            Dict[str, Any]: 导出结果信息
        """
        if not options.output_path:
            raise ValueError("GeoJSON导出需要指定输出路径")
        
        # 确保输出目录存在
        options.output_path.parent.mkdir(parents=True, exist_ok=True)
        
        if isinstance(data, ParseResult):
            data = [data]
        
        # 构建GeoJSON结构
        geojson = {
            'type': 'FeatureCollection',
            'crs': {
                'type': 'name',
                'properties': {
                    'name': 'EPSG:4326'
                }
            },
            'features': []
        }
        
        for result in data:
            if result.station_info and result.station_info.latitude and result.station_info.longitude:
                feature = {
                    'type': 'Feature',
                    'geometry': {
                        'type': 'Point',
                        'coordinates': [
                            float(result.station_info.longitude),
                            float(result.station_info.latitude)
                        ]
                    },
                    'properties': {
                        'station_id': result.station_info.station_id,
                        'altitude': getattr(result.station_info, 'observation_altitude', getattr(result.station_info, 'altitude', 0)),
                        'file_name': getattr(result, 'file_name', getattr(result, 'file_path', 'unknown')),
                        'observations_count': len(getattr(result, 'elements', None) or getattr(result, 'observations', None) or [])
                    }
                }
                
                # 添加最新观测数据
                elements = getattr(result, 'elements', None) or getattr(result, 'observations', None)
                if elements:
                    latest_obs = elements[-1]
                    if hasattr(latest_obs, '__dict__'):
                        feature['properties']['latest_observation'] = latest_obs.__dict__
                
                geojson['features'].append(feature)
        
        # 写入GeoJSON文件
        with open(options.output_path, 'w', encoding=options.encoding) as f:
            json.dump(
                geojson,
                f,
                indent=self.indent,
                ensure_ascii=self.ensure_ascii,
                cls=self.WeatherDataJSONEncoder
            )
        
        return {
            'format': 'geojson',
            'output_path': str(options.output_path),
            'features_count': len(geojson['features']),
            'encoding': options.encoding
        } 