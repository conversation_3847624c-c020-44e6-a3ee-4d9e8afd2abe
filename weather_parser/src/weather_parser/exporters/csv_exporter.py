"""
CSV导出器

实现CSV格式的数据导出，适合Excel等工具读取
"""

import csv
from typing import Any, Dict, List, Union
from pathlib import Path
import logging
from datetime import datetime

from .base_exporter import BaseExporter, ExportOptions
from ..models import ParseResult, StationInfo, ElementData

logger = logging.getLogger(__name__)


class CSVExporter(BaseExporter):
    """
    CSV导出器
    
    将气象数据导出为CSV格式，支持扁平化和嵌套结构
    """
    
    def __init__(self):
        super().__init__("CSVExporter")
        self.delimiter = ','
        self.quotechar = '"'
        self.quoting = csv.QUOTE_MINIMAL
    
    def export(
        self,
        data: Union[ParseResult, List[ParseResult], Dict[str, Any]],
        options: ExportOptions
    ) -> Dict[str, Any]:
        """
        导出数据为CSV格式
        
        Args:
            data: 要导出的数据
            options: 导出选项
            
        Returns:
            Dict[str, Any]: 导出结果信息
        """
        self.validate_options(options)
        
        if not options.output_path:
            raise ValueError("CSV导出需要指定输出路径")
        
        # 确保输出目录存在
        options.output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 准备数据
        prepared_data = self.prepare_data(data, options)
        
        # 根据数据类型选择导出方式
        if isinstance(data, ParseResult):
            result = self._export_single_result(prepared_data, options)
        elif isinstance(data, list):
            result = self._export_batch_results(prepared_data, options)
        else:
            result = self._export_dict_data(prepared_data, options)
        
        self._update_statistics()
        
        self.logger.info(f"CSV导出完成: {options.output_path}")
        
        return result
    
    def get_supported_extensions(self) -> List[str]:
        """获取支持的文件扩展名"""
        return ['.csv']
    
    def _export_single_result(
        self,
        data: Dict[str, Any],
        options: ExportOptions
    ) -> Dict[str, Any]:
        """导出单个解析结果"""
        rows = []
        
        # 处理观测数据或要素数据
        obs_data = data.get('observations', []) or data.get('elements', [])
        if obs_data:
            # 获取所有字段名
            all_fields = set()
            for obs in obs_data:
                all_fields.update(obs.keys())
            
            # 添加台站信息字段
            if 'station_info' in data:
                station_fields = {f"station_{k}": v 
                                for k, v in data['station_info'].items()}
                all_fields.update(station_fields.keys())
            
            # 添加文件信息字段
            if 'file_info' in data:
                file_fields = {f"file_{k}": v 
                             for k, v in data['file_info'].items()}
                all_fields.update(file_fields.keys())
            
            fieldnames = sorted(all_fields)
            
            # 写入CSV
            with open(options.output_path, 'w', 
                     newline='', encoding=options.encoding) as csvfile:
                writer = csv.DictWriter(
                    csvfile,
                    fieldnames=fieldnames,
                    delimiter=self.delimiter,
                    quotechar=self.quotechar,
                    quoting=self.quoting
                )
                
                writer.writeheader()
                
                for obs in obs_data:
                    row = obs.copy()
                    
                    # 添加台站信息
                    if 'station_info' in data:
                        row.update({f"station_{k}": v 
                                  for k, v in data['station_info'].items()})
                    
                    # 添加文件信息
                    if 'file_info' in data:
                        row.update({f"file_{k}": v 
                                  for k, v in data['file_info'].items()})
                    
                    # 扁平化复杂对象
                    row = self._flatten_dict(row)
                    
                    writer.writerow(row)
                    rows.append(row)
        
        result = {
            'format': 'csv',
            'output_path': str(options.output_path),
            'rows_written': len(rows),
            'files_processed': 1,
            'encoding': options.encoding
        }
        
        return result
    
    def _export_batch_results(
        self,
        data: Dict[str, Any],
        options: ExportOptions
    ) -> Dict[str, Any]:
        """导出批量结果"""
        all_rows = []
        all_fields = set()
        
        # 收集所有数据行和字段
        for result_data in data['results']:
            if 'observations' in result_data and result_data['observations']:
                for obs in result_data['observations']:
                    row = obs.copy()
                    
                    # 添加台站信息
                    if 'station_info' in result_data:
                        row.update({f"station_{k}": v 
                                  for k, v in result_data['station_info'].items()})
                    
                    # 添加文件信息
                    if 'file_info' in result_data:
                        row.update({f"file_{k}": v 
                                  for k, v in result_data['file_info'].items()})
                    
                    # 扁平化复杂对象
                    row = self._flatten_dict(row)
                    
                    all_rows.append(row)
                    all_fields.update(row.keys())
        
        fieldnames = sorted(all_fields)
        
        # 写入CSV
        with open(options.output_path, 'w', 
                 newline='', encoding=options.encoding) as csvfile:
            writer = csv.DictWriter(
                csvfile,
                fieldnames=fieldnames,
                delimiter=self.delimiter,
                quotechar=self.quotechar,
                quoting=self.quoting
            )
            
            writer.writeheader()
            writer.writerows(all_rows)
        
        return {
            'format': 'csv',
            'output_path': str(options.output_path),
            'files_processed': data['batch_info']['total_files'],
            'rows_exported': len(all_rows),
            'columns': len(fieldnames),
            'encoding': options.encoding
        }
    
    def _export_dict_data(
        self,
        data: Dict[str, Any],
        options: ExportOptions
    ) -> Dict[str, Any]:
        """导出字典数据"""
        # 扁平化数据
        flattened = self._flatten_dict(data)
        
        with open(options.output_path, 'w', 
                 newline='', encoding=options.encoding) as csvfile:
            writer = csv.DictWriter(
                csvfile,
                fieldnames=flattened.keys(),
                delimiter=self.delimiter,
                quotechar=self.quotechar,
                quoting=self.quoting
            )
            
            writer.writeheader()
            writer.writerow(flattened)
        
        return {
            'format': 'csv',
            'output_path': str(options.output_path),
            'rows_exported': 1,
            'columns': len(flattened),
            'encoding': options.encoding
        }
    
    def _flatten_dict(
        self,
        data: Dict[str, Any],
        prefix: str = '',
        separator: str = '_'
    ) -> Dict[str, Any]:
        """
        扁平化嵌套字典
        
        Args:
            data: 要扁平化的字典
            prefix: 前缀
            separator: 分隔符
            
        Returns:
            Dict[str, Any]: 扁平化后的字典
        """
        flattened = {}
        
        for key, value in data.items():
            new_key = f"{prefix}{separator}{key}" if prefix else key
            
            if isinstance(value, dict):
                flattened.update(
                    self._flatten_dict(value, new_key, separator)
                )
            elif isinstance(value, list):
                # 将列表转换为字符串或展开
                if value and isinstance(value[0], dict):
                    # 如果是字典列表，为每个元素创建索引
                    for i, item in enumerate(value):
                        if isinstance(item, dict):
                            flattened.update(
                                self._flatten_dict(item, f"{new_key}_{i}", separator)
                            )
                        else:
                            flattened[f"{new_key}_{i}"] = self._format_value(item)
                else:
                    # 简单列表转为字符串
                    flattened[new_key] = str(value)
            else:
                flattened[new_key] = self._format_value(value)
        
        return flattened
    
    def _format_value(self, value: Any) -> str:
        """
        格式化值
        
        Args:
            value: 要格式化的值
            
        Returns:
            str: 格式化后的字符串
        """
        if value is None:
            return ''
        elif isinstance(value, bool):
            return 'true' if value else 'false'
        elif isinstance(value, (int, float)):
            return str(value)
        else:
            return str(value)
    
    def export_metadata_only(
        self,
        data: Union[ParseResult, List[ParseResult]],
        options: ExportOptions
    ) -> Dict[str, Any]:
        """
        仅导出元数据
        
        Args:
            data: 解析结果
            options: 导出选项
            
        Returns:
            Dict[str, Any]: 导出结果
        """
        metadata_rows = []
        
        if isinstance(data, ParseResult):
            data = [data]
        
        for result in data:
            row = {
                'file_name': getattr(result, 'file_name', getattr(result, 'file_path', 'unknown')),
                'file_size': getattr(result, 'file_size', 0),
                'parse_time': getattr(result, 'parse_time', datetime.now()).strftime(options.date_format),
                'success': getattr(result, 'success', False),
                'observations_count': len(getattr(result, 'elements', None) or getattr(result, 'observations', None) or []),
                'warnings_count': len(getattr(result, 'warnings', [])),
                'errors_count': len(getattr(result, 'errors', []))
            }
            
            # 添加台站信息
            if result.station_info:
                row.update({
                    'station_id': result.station_info.station_id,
                    'station_name': getattr(result.station_info, 'station_name', ''),
                    'latitude': result.station_info.latitude,
                    'longitude': result.station_info.longitude,
                    'altitude': getattr(result.station_info, 'observation_altitude', getattr(result.station_info, 'altitude', 0))
                })
            
            metadata_rows.append(row)
        
        # 写入CSV
        if metadata_rows:
            with open(options.output_path, 'w', 
                     newline='', encoding=options.encoding) as csvfile:
                writer = csv.DictWriter(
                    csvfile,
                    fieldnames=metadata_rows[0].keys(),
                    delimiter=self.delimiter,
                    quotechar=self.quotechar,
                    quoting=self.quoting
                )
                
                writer.writeheader()
                writer.writerows(metadata_rows)
        
        return {
            'format': 'csv',
            'output_path': str(options.output_path),
            'rows_exported': len(metadata_rows),
            'columns': len(metadata_rows[0].keys()) if metadata_rows else 0,
            'encoding': options.encoding
        } 