"""
数据库表结构定义

定义气象数据的数据库表结构
"""
import logging
from typing import Dict, List, Optional
from .connection import DatabaseConnection

logger = logging.getLogger(__name__)


class WeatherDataSchema:
    """气象数据库表结构管理器"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db = db_connection
        self.db_type = db_connection.config.db_type.lower()
    
    def create_tables(self):
        """创建所有数据表"""
        try:
            # 创建台站信息表
            self._create_stations_table()
            
            # 创建文件信息表
            self._create_files_table()
            
            # 创建观测数据表
            self._create_observations_table()
            
            # 创建气象要素表
            self._create_elements_table()
            
            # 创建数据质量表
            self._create_quality_table()
            
            # 创建索引
            self._create_indexes()
            
            logger.info("数据表创建完成")
            
        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            raise
    
    def _create_stations_table(self):
        """创建台站信息表"""
        if self.db_type == 'sqlite':
            sql = """
            CREATE TABLE IF NOT EXISTS stations (
                station_id TEXT PRIMARY KEY,
                latitude REAL NOT NULL,
                longitude REAL NOT NULL,
                observation_altitude REAL NOT NULL,
                station_altitude REAL,
                station_name TEXT,
                province TEXT,
                city TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
        else:  # PostgreSQL/MySQL
            sql = """
            CREATE TABLE IF NOT EXISTS stations (
                station_id VARCHAR(20) PRIMARY KEY,
                latitude DECIMAL(10, 6) NOT NULL,
                longitude DECIMAL(10, 6) NOT NULL,
                observation_altitude DECIMAL(8, 2) NOT NULL,
                station_altitude DECIMAL(8, 2),
                station_name VARCHAR(100),
                province VARCHAR(50),
                city VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
        
        self.db.execute(sql)
    
    def _create_files_table(self):
        """创建文件信息表"""
        if self.db_type == 'sqlite':
            sql = """
            CREATE TABLE IF NOT EXISTS files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_path TEXT NOT NULL UNIQUE,
                file_name TEXT NOT NULL,
                file_size INTEGER,
                format_type TEXT,
                header_info TEXT,
                parse_time REAL,
                record_count INTEGER,
                success BOOLEAN DEFAULT TRUE,
                error_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
        else:
            sql = """
            CREATE TABLE IF NOT EXISTS files (
                id SERIAL PRIMARY KEY,
                file_path VARCHAR(500) NOT NULL UNIQUE,
                file_name VARCHAR(255) NOT NULL,
                file_size BIGINT,
                format_type VARCHAR(50),
                header_info TEXT,
                parse_time DECIMAL(10, 4),
                record_count INTEGER,
                success BOOLEAN DEFAULT TRUE,
                error_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
        
        self.db.execute(sql)
    
    def _create_observations_table(self):
        """创建观测数据表"""
        if self.db_type == 'sqlite':
            sql = """
            CREATE TABLE IF NOT EXISTS observations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id INTEGER NOT NULL,
                station_id TEXT NOT NULL,
                observation_time TIMESTAMP NOT NULL,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                day INTEGER NOT NULL,
                hour INTEGER NOT NULL,
                data_json TEXT,
                element_count INTEGER DEFAULT 0,
                quality_grade TEXT,
                quality_score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (file_id) REFERENCES files(id),
                FOREIGN KEY (station_id) REFERENCES stations(station_id)
            )
            """
        else:
            sql = """
            CREATE TABLE IF NOT EXISTS observations (
                id SERIAL PRIMARY KEY,
                file_id INTEGER NOT NULL,
                station_id VARCHAR(20) NOT NULL,
                observation_time TIMESTAMP NOT NULL,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                day INTEGER NOT NULL,
                hour INTEGER NOT NULL,
                data_json TEXT,
                element_count INTEGER DEFAULT 0,
                quality_grade VARCHAR(5),
                quality_score DECIMAL(5, 2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (file_id) REFERENCES files(id),
                FOREIGN KEY (station_id) REFERENCES stations(station_id)
            )
            """
        
        self.db.execute(sql)
    
    def _create_elements_table(self):
        """创建气象要素表"""
        if self.db_type == 'sqlite':
            sql = """
            CREATE TABLE IF NOT EXISTS elements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                observation_id INTEGER NOT NULL,
                element_type TEXT NOT NULL,
                element_code TEXT NOT NULL,
                element_value TEXT,
                numeric_value REAL,
                unit TEXT,
                quality_code TEXT,
                description TEXT,
                is_missing BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (observation_id) REFERENCES observations(id)
            )
            """
        else:
            sql = """
            CREATE TABLE IF NOT EXISTS elements (
                id SERIAL PRIMARY KEY,
                observation_id INTEGER NOT NULL,
                element_type VARCHAR(50) NOT NULL,
                element_code VARCHAR(20) NOT NULL,
                element_value TEXT,
                numeric_value DECIMAL(15, 4),
                unit VARCHAR(20),
                quality_code VARCHAR(10),
                description TEXT,
                is_missing BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (observation_id) REFERENCES observations(id)
            )
            """
        
        self.db.execute(sql)
    
    def _create_quality_table(self):
        """创建数据质量表"""
        if self.db_type == 'sqlite':
            sql = """
            CREATE TABLE IF NOT EXISTS data_quality (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                observation_id INTEGER NOT NULL,
                total_elements INTEGER DEFAULT 0,
                valid_elements INTEGER DEFAULT 0,
                missing_elements INTEGER DEFAULT 0,
                invalid_elements INTEGER DEFAULT 0,
                completeness_score REAL DEFAULT 0,
                consistency_score REAL DEFAULT 0,
                reliability_score REAL DEFAULT 0,
                overall_score REAL DEFAULT 0,
                quality_grade TEXT,
                issues TEXT,
                checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (observation_id) REFERENCES observations(id)
            )
            """
        else:
            sql = """
            CREATE TABLE IF NOT EXISTS data_quality (
                id SERIAL PRIMARY KEY,
                observation_id INTEGER NOT NULL,
                total_elements INTEGER DEFAULT 0,
                valid_elements INTEGER DEFAULT 0,
                missing_elements INTEGER DEFAULT 0,
                invalid_elements INTEGER DEFAULT 0,
                completeness_score DECIMAL(5, 2) DEFAULT 0,
                consistency_score DECIMAL(5, 2) DEFAULT 0,
                reliability_score DECIMAL(5, 2) DEFAULT 0,
                overall_score DECIMAL(5, 2) DEFAULT 0,
                quality_grade VARCHAR(5),
                issues TEXT,
                checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (observation_id) REFERENCES observations(id)
            )
            """
        
        self.db.execute(sql)
    
    def _create_indexes(self):
        """创建数据库索引"""
        indexes = [
            # 台站索引
            "CREATE INDEX IF NOT EXISTS idx_stations_location ON stations(latitude, longitude)",
            
            # 文件索引
            "CREATE INDEX IF NOT EXISTS idx_files_name ON files(file_name)",
            "CREATE INDEX IF NOT EXISTS idx_files_created ON files(created_at)",
            
            # 观测数据索引
            "CREATE INDEX IF NOT EXISTS idx_observations_station ON observations(station_id)",
            "CREATE INDEX IF NOT EXISTS idx_observations_time ON observations(observation_time)",
            "CREATE INDEX IF NOT EXISTS idx_observations_date ON observations(year, month, day)",
            "CREATE INDEX IF NOT EXISTS idx_observations_file ON observations(file_id)",
            
            # 要素索引
            "CREATE INDEX IF NOT EXISTS idx_elements_observation ON elements(observation_id)",
            "CREATE INDEX IF NOT EXISTS idx_elements_type ON elements(element_type)",
            "CREATE INDEX IF NOT EXISTS idx_elements_code ON elements(element_code)",
            "CREATE INDEX IF NOT EXISTS idx_elements_value ON elements(numeric_value)",
            
            # 质量索引
            "CREATE INDEX IF NOT EXISTS idx_quality_observation ON data_quality(observation_id)",
            "CREATE INDEX IF NOT EXISTS idx_quality_grade ON data_quality(quality_grade)",
            "CREATE INDEX IF NOT EXISTS idx_quality_score ON data_quality(overall_score)"
        ]
        
        for index_sql in indexes:
            try:
                self.db.execute(index_sql)
            except Exception as e:
                # 某些数据库可能不支持IF NOT EXISTS
                if "already exists" not in str(e).lower():
                    logger.warning(f"创建索引失败: {index_sql}, 错误: {e}")
    
    def drop_tables(self):
        """删除所有数据表（谨慎使用）"""
        tables = ['data_quality', 'elements', 'observations', 'files', 'stations']
        
        for table in tables:
            try:
                self.db.execute(f"DROP TABLE IF EXISTS {table}")
                logger.info(f"删除表: {table}")
            except Exception as e:
                logger.error(f"删除表{table}失败: {e}")
    
    def get_table_info(self) -> Dict[str, List[Dict]]:
        """获取表结构信息"""
        tables = ['stations', 'files', 'observations', 'elements', 'data_quality']
        table_info = {}
        
        for table in tables:
            try:
                if self.db_type == 'sqlite':
                    sql = f"PRAGMA table_info({table})"
                elif self.db_type == 'postgresql':
                    sql = f"""
                    SELECT column_name, data_type, is_nullable 
                    FROM information_schema.columns 
                    WHERE table_name = '{table}'
                    """
                else:  # mysql
                    sql = f"DESCRIBE {table}"
                
                result = self.db.fetchall(sql)
                table_info[table] = result
                
            except Exception as e:
                logger.error(f"获取表{table}信息失败: {e}")
                table_info[table] = []
        
        return table_info
    
    def get_table_statistics(self) -> Dict[str, int]:
        """获取表记录统计"""
        tables = ['stations', 'files', 'observations', 'elements', 'data_quality']
        stats = {}
        
        for table in tables:
            try:
                count = self.db.fetchone(f"SELECT COUNT(*) as count FROM {table}")
                if self.db_type == 'sqlite':
                    stats[table] = count[0] if count else 0
                else:
                    stats[table] = count['count'] if count else 0
            except Exception as e:
                logger.error(f"获取表{table}统计失败: {e}")
                stats[table] = 0
        
        return stats 