#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象数据处理系统测试脚本
用于验证系统各个组件的功能
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# 导入系统模块
from database.database import create_tables, get_db_connection
from data_validator import WeatherDataValidator
from data_exporter import WeatherDataExporter
import config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemTester:
    def __init__(self):
        self.test_results = []
        self.validator = None
        self.exporter = None
    
    def log_test_result(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status} {test_name}"
        if message:
            result += f" - {message}"
        
        self.test_results.append((test_name, success, message))
        print(result)
        logger.info(result)
    
    def test_config(self):
        """测试配置模块"""
        try:
            config.init_config()
            
            # 测试配置获取
            db_file = config.get_config('DATABASE', 'database_file')
            log_level = config.get_config('LOGGING', 'level')
            
            # 测试气象要素信息
            temp_info = config.get_element_info('T')
            
            success = (db_file is not None and 
                      log_level is not None and 
                      temp_info.get('name') == '气温')
            
            self.log_test_result("配置模块", success, 
                               f"数据库: {db_file}, 日志级别: {log_level}")
            return success
        except Exception as e:
            self.log_test_result("配置模块", False, str(e))
            return False
    
    def test_database(self):
        """测试数据库模块"""
        try:
            # 创建数据表
            create_tables()
            
            # 测试数据库连接
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = ['hourly_pressure', 'daily_pressure', 
                             'hourly_temperature', 'daily_temperature']
            
            success = all(table in tables for table in expected_tables)
            
            conn.close()
            
            self.log_test_result("数据库模块", success, 
                               f"创建了 {len(tables)} 个数据表")
            return success
        except Exception as e:
            self.log_test_result("数据库模块", False, str(e))
            return False
    
    def test_data_validator(self):
        """测试数据验证模块"""
        try:
            self.validator = WeatherDataValidator()
            
            # 测试获取站点列表
            stations = self.validator.get_station_list()
            
            # 测试数据完整性检查
            if stations:
                completeness = self.validator.check_data_completeness(station_id=stations[0])
                quality_issues = self.validator.check_data_quality(station_id=stations[0])
                
                success = (isinstance(completeness, dict) and 
                          isinstance(quality_issues, list))
                
                self.log_test_result("数据验证模块", success, 
                                   f"找到 {len(stations)} 个站点")
            else:
                self.log_test_result("数据验证模块", True, "数据库中暂无数据")
                success = True
            
            return success
        except Exception as e:
            self.log_test_result("数据验证模块", False, str(e))
            return False
    
    def test_data_exporter(self):
        """测试数据导出模块"""
        try:
            self.exporter = WeatherDataExporter()
            
            # 测试获取站点列表
            stations = self.exporter.get_available_stations()
            
            if stations:
                # 测试数据查询
                station_id = stations[0]
                hourly_data = self.exporter.query_hourly_data(
                    station_id, elements=['temperature', 'pressure'])
                
                # 测试导出功能
                if not hourly_data.empty:
                    test_file = f"test_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                    export_success = self.exporter.export_to_csv(hourly_data, test_file)
                    
                    # 清理测试文件
                    if os.path.exists(test_file):
                        os.remove(test_file)
                    
                    success = export_success
                    message = f"查询到 {len(hourly_data)} 条记录"
                else:
                    success = True
                    message = "数据查询正常，但无数据"
            else:
                success = True
                message = "数据库中暂无数据"
            
            self.log_test_result("数据导出模块", success, message)
            return success
        except Exception as e:
            self.log_test_result("数据导出模块", False, str(e))
            return False
    
    def test_file_processing(self):
        """测试文件处理功能"""
        try:
            # 查找A文件
            a_files = [f for f in os.listdir('.') if f.startswith('A') and f.endswith('.TXT')]
            
            if not a_files:
                self.log_test_result("文件处理", True, "未找到A文件，跳过测试")
                return True
            
            # 测试文件解析（不实际处理，只检查文件格式）
            test_file = a_files[0]
            
            try:
                with open(test_file, 'r', encoding='gbk') as f:
                    content = f.read()
                
                # 检查文件头格式
                header = content.split('\n', 1)[0]
                parts = header.split()
                
                success = (len(parts) >= 12 and 
                          parts[0].isdigit() and 
                          len(parts[0]) == 5)
                
                message = f"文件 {test_file} 格式检查"
                
            except Exception as e:
                success = False
                message = f"文件读取失败: {str(e)}"
            
            self.log_test_result("文件处理", success, message)
            return success
        except Exception as e:
            self.log_test_result("文件处理", False, str(e))
            return False
    
    def test_system_integration(self):
        """测试系统集成"""
        try:
            # 检查数据库中是否有数据
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM hourly_temperature")
            temp_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM hourly_pressure")
            pressure_count = cursor.fetchone()[0]
            
            conn.close()
            
            # 如果有数据，测试数据一致性
            if temp_count > 0 and pressure_count > 0:
                success = True
                message = f"温度记录: {temp_count}, 气压记录: {pressure_count}"
            else:
                success = True
                message = "数据库结构正常，但暂无观测数据"
            
            self.log_test_result("系统集成", success, message)
            return success
        except Exception as e:
            self.log_test_result("系统集成", False, str(e))
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("气象数据处理系统测试")
        print("=" * 60)
        
        tests = [
            ("配置模块", self.test_config),
            ("数据库模块", self.test_database),
            ("数据验证模块", self.test_data_validator),
            ("数据导出模块", self.test_data_exporter),
            ("文件处理", self.test_file_processing),
            ("系统集成", self.test_system_integration),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                logger.error(f"测试 {test_name} 时发生异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"测试完成: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！系统运行正常。")
        else:
            print("⚠️  部分测试失败，请检查系统配置。")
        
        print("=" * 60)
        
        return passed == total
    
    def cleanup(self):
        """清理测试资源"""
        if self.validator:
            del self.validator
        if self.exporter:
            del self.exporter

def main():
    """主函数"""
    tester = SystemTester()
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        sys.exit(1)
    finally:
        tester.cleanup()

if __name__ == '__main__':
    main()
