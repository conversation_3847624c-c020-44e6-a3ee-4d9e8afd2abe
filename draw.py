import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.colors as colors
import matplotlib as mpl # 添加导入
from matplotlib.path import Path
from matplotlib.patches import PathPatch # 导入 PathPatch
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from cartopy.mpl.gridliner import LONGITUDE_FORMATTER, LATITUDE_FORMATTER
from scipy.interpolate import Rbf  # 使用Rbf插值
import cartopy.io.shapereader as shpreader # 添加导入
import os
import io # 添加导入
import rasterio # 添加导入以读取高程数据
from matplotlib.colors import LightSource, ListedColormap # 添加导入用于地形晕渲

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 修改剪裁函数，使用 cartopy.io.shapereader
def shp2clip(originfig, ax, shp_fname):
    """ 使用 shapefile 对图像进行剪裁 """
    from cartopy.mpl.patch import geos_to_path
    try:
        reader = shpreader.Reader(shp_fname)
        geometries = list(reader.geometries()) # 获取所有地理对象
        reader.close() # 关闭读取器

        if not geometries:
            print(f"警告：在 {shp_fname} 中未找到地理对象。")
            return

        # 从所有地理对象创建复合路径
        # 注意：geos_to_path 可能期望单一对象或特定结构，这里尝试直接传递列表
        path = Path.make_compound_path(*geos_to_path(geometries))

        # 创建剪裁路径补丁
        patch = PathPatch(path, transform=ax.transData)

        # 应用剪裁 - 直接应用到 ContourSet 对象
        originfig.set_clip_path(patch) # 移除循环，直接应用

    except Exception as e:
        print(f"从 {shp_fname} 创建或应用剪裁路径时出错: {e}")
        # 可以根据具体错误添加备用处理逻辑

# 读取DEM高程数据函数
def read_dem_data(dem_file):
    """读取DEM数据并返回高程数据和坐标信息"""
    try:
        with rasterio.open(dem_file) as dem:
            elevation = dem.read(1)
            # 处理NoData值
            elevation_masked = np.ma.masked_where(elevation == dem.nodata, elevation)
            transform = dem.transform
            dem_extent = [dem.bounds.left, dem.bounds.right, 
                         dem.bounds.bottom, dem.bounds.top]
            
            # 创建与DEM匹配的经纬度网格
            rows, cols = np.indices((dem.height, dem.width))
            xs, ys = rasterio.transform.xy(transform, rows.flatten(), cols.flatten())
            lon_grid = np.array(xs).reshape(dem.height, dem.width)
            lat_grid = np.array(ys).reshape(dem.height, dem.width)
            
            return elevation_masked, lon_grid, lat_grid, dem_extent, dem.height, dem.width
    except Exception as e:
        print(f"读取DEM数据时发生错误: {e}")
        return None, None, None, None, None, None

# 修改绘图函数，添加高程数据处理
def generate_rainfall_map(rain_data: pd.DataFrame, dem_file='高程/target_region_dem.tif'):
    # 读取DEM高程数据
    print("读取DEM高程数据...")
    elevation_masked, dem_lon_grid, dem_lat_grid, dem_extent, dem_height, dem_width = read_dem_data(dem_file)
    
    if elevation_masked is None:
        print("无法读取DEM数据，将继续尝试生成仅包含降水的地图。")
    
    # 提取站点数据和坐标
    lon = rain_data['经度'].values
    lat = rain_data['纬度'].values
    values = rain_data['总降雨量'].values

    # 过滤掉无效的经纬度或降雨量数据 (例如 NaN)
    valid_indices = ~np.isnan(lon) & ~np.isnan(lat) & ~np.isnan(values)
    lon = lon[valid_indices]
    lat = lat[valid_indices]
    values = values[valid_indices]

    if len(lon) < 3:
        print("错误：有效的站点数据不足（少于3个），无法进行插值。")
        return None # 明确返回 None

    # 使用DEM数据范围来确定插值范围（如果可用）
    if dem_extent:
        x_min, x_max = dem_extent[0], dem_extent[1]
        y_min, y_max = dem_extent[2], dem_extent[3]
    else:
        # 原始插值范围定义 - 确保涵盖整个平谷区
        x_min, x_max = 116.75, 117.6
        y_min, y_max = 39.95, 40.4

    # 创建网格
    if dem_lon_grid is None or dem_lat_grid is None:
        grid_resolution = 300j
        grid_x, grid_y = np.mgrid[x_min:x_max:grid_resolution, y_min:y_max:grid_resolution]
    else:
        # 直接使用DEM的网格
        grid_x, grid_y = dem_lon_grid, dem_lat_grid

    # 使用Rbf方法进行插值
    try:
        rbf = Rbf(lon, lat, values, function='linear')
        grid_z = rbf(grid_x, grid_y)
    except ValueError as e:
        print(f"插值错误: {e}. 请检查输入数据。")
        # 可能由于数据点共线或数量不足导致
        return None # 明确返回 None
    except Exception as e: # 捕捉其他潜在的插值错误
        print(f"插值时发生未知错误: {e}")
        return None

    # 创建画布
    fig = plt.figure(figsize=(12, 9))
    ax = plt.axes(projection=ccrs.PlateCarree())
    ax.set_facecolor('white') # 设置背景色为白色

    # 设置地图范围
    ax.set_extent([x_min, x_max, y_min, y_max], crs=ccrs.PlateCarree())

    # 添加地图特征
    ax.add_feature(cfeature.BORDERS, linestyle=':')
    ax.add_feature(cfeature.COASTLINE)
    ax.add_feature(cfeature.RIVERS)

    # 添加网格线
    gl = ax.gridlines(crs=ccrs.PlateCarree(), draw_labels=True,
                     linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    gl.top_labels = False
    gl.right_labels = False
    gl.xformatter = LONGITUDE_FORMATTER
    gl.yformatter = LATITUDE_FORMATTER
    
    # --- 添加地形晕渲处理 ---
    if elevation_masked is not None:
        # 设置地形晕渲绘图参数
        vmin_gray = 0
        vmax_gray = 1500  # 根据实际高程数据范围调整
        
        # 创建光源对象
        ls = LightSource(azdeg=315, altdeg=45)
        
        # 计算基于高程数据的晕渲RGB数组
        rgb = ls.shade(elevation_masked, cmap=plt.get_cmap('Greys'),
                      vert_exag=1, blend_mode='soft')
        
        # 显示晕渲RGB数组
        terrain_plot = ax.imshow(rgb, origin='upper', extent=dem_extent,
                               transform=ccrs.PlateCarree(), zorder=1,
                               interpolation='bilinear')
        
        # 裁剪地形晕渲图
        watershed_shp = '地图/流域.shp'
        if os.path.exists(watershed_shp):
            print("裁剪地形晕渲图...")
            shp2clip(terrain_plot, ax, watershed_shp)
        else:
            print(f"警告: 剪裁文件 {watershed_shp} 不存在。")

    # 颜色范围和色系
    clevs = [0.1, 10, 25, 50, 100, 250, 500]  # 自定义颜色列表
    cdict = ['#A9F090', '#40B73F', '#63B7FF', '#0000FE', '#FF00FC', '#850042']
    my_cmap = colors.ListedColormap(cdict)
    norm = mpl.colors.BoundaryNorm(clevs, my_cmap.N) # 使用 mpl.colors

    # 绘制降水等值线填充图，提高透明度，以便显示下层的地形晕渲
    contour = ax.contourf(grid_x, grid_y, grid_z, levels=clevs,
                         cmap=my_cmap, norm=norm, extend='max',
                         transform=ccrs.PlateCarree(), alpha=0.7, zorder=2)  # 设置透明度和zorder

    # 应用裁剪
    watershed_shp = '地图/流域.shp'
    pinggu_shp = '地图/pinggu.shp'

    # 检查 shapefile 是否存在
    if os.path.exists(watershed_shp):
        shp2clip(contour, ax, watershed_shp) # 应用剪裁
    else:
        print(f"警告: 剪裁文件 {watershed_shp} 不存在。")

    if os.path.exists(pinggu_shp):
         # 使用 add_geometries 绘制平谷边界
        try:
            pinggu_reader = shpreader.Reader(pinggu_shp)
            pinggu_geoms = list(pinggu_reader.geometries())
            pinggu_reader.close()
            if pinggu_geoms:
                 ax.add_geometries(pinggu_geoms, crs=ccrs.PlateCarree(),
                                   facecolor='none', edgecolor='black', linewidth=1.5, zorder=3)
            else:
                 print(f"警告：在 {pinggu_shp} 中未找到用于绘制边界的地理对象。")
        except Exception as e:
            print(f"读取或绘制边界文件 {pinggu_shp} 时出错: {e}")
    else:
        print(f"警告: 边界文件 {pinggu_shp} 不存在。")

    # 添加绘制流域边界的代码
    if os.path.exists(watershed_shp):
        try:
            watershed_reader = shpreader.Reader(watershed_shp)
            watershed_geoms = list(watershed_reader.geometries())
            watershed_reader.close()
            if watershed_geoms:
                ax.add_geometries(watershed_geoms, crs=ccrs.PlateCarree(),
                                  facecolor='none', edgecolor='blue', linewidth=1.0, zorder=4) # 使用蓝色绘制流域边界
            else:
                print(f"警告：在 {watershed_shp} 中未找到用于绘制边界的地理对象。")
        except Exception as e:
            print(f"读取或绘制边界文件 {watershed_shp} 时出错: {e}")
    else:
        print(f"警告: 流域边界文件 {watershed_shp} 不存在。")

    # --- 在地图上标注平谷区各乡镇最大降雨量站点 ---
    try:
        pinggu_data = rain_data[rain_data['区县'] == '平谷'].copy()
        # 确保 '乡镇' 和 '站名' 列存在
        if '乡镇' in pinggu_data.columns and '站名' in pinggu_data.columns:
            # 按乡镇分组，找到每个乡镇总降雨量最大的站点的索引
            max_rain_indices = pinggu_data.loc[pinggu_data.groupby('乡镇')['总降雨量'].idxmax()]

            # 遍历这些站点并在地图上添加标注
            for index, row in max_rain_indices.iterrows():
                lon_label = row['经度']
                lat_label = row['纬度']
                rain_value = row['总降雨量']
                station_name = row['站名']

                # 检查坐标是否有效
                if pd.notna(lon_label) and pd.notna(lat_label) and pd.notna(rain_value):
                    # 添加散点标记站点位置
                    ax.scatter(lon_label, lat_label, s=10, color='red', zorder=11, transform=ccrs.PlateCarree())
                    # label_text = f"{station_name}\n{rain_value:.1f} mm" # 原格式
                    label_text = f"{station_name}\n{rain_value:.1f}" # 分两行显示，移除单位
                    ax.text(lon_label, lat_label, label_text,
                            transform=ccrs.PlateCarree(),
                            fontsize=7, # 字体大小
                            color='black', # 字体颜色
                            ha='center', # 水平居中
                            va='bottom', # 垂直对齐: 文本在点上方
                            # bbox=dict(boxstyle='round,pad=0.2', fc='white', alpha=0.6), # 移除背景框
                            zorder=10) # 确保标注在散点下方但在其他图层上方
        else:
            print("警告：数据中缺少 '乡镇' 或 '站名' 列，无法进行标注。")

    except KeyError as e:
        print(f"警告：数据处理中发生键错误 '{e}'，可能缺少必要的列，无法进行标注。")
    except Exception as e:
        print(f"标注乡镇最大降雨量站点时发生错误: {e}")
    # --- 标注结束 ---

    # --- 标注其他区县最大降雨量站点 (且 区域内站点 == '是') ---
    try:
        other_data = rain_data[rain_data['区县'] != '平谷'].copy()
        # 检查必要的列是否存在
        required_cols_other = ['区县', '总降雨量', '区域内站点', '站名', '经度', '纬度']

        if all(col in other_data.columns for col in required_cols_other):
            # --- 核心修改：先筛选 '区域内站点' == '是' --- 
            watershed_stations_other = other_data[other_data['区域内站点'] == '是'].copy()

            if not watershed_stations_other.empty:
                # 按区县分组 (在已筛选的数据上分组)
                grouped_other = watershed_stations_other.groupby('区县')
                for name, group in grouped_other:
                    if not group.empty:
                        # 找到该区县(已筛选)总降雨量最大的站点
                        try:
                            max_station_idx = group['总降雨量'].idxmax()
                            max_station_row = group.loc[max_station_idx]

                            # 提取信息进行标注 (只要找到最大值就标注)
                            lon_label = max_station_row['经度']
                            lat_label = max_station_row['纬度']
                            rain_value = max_station_row['总降雨量']
                            station_name = max_station_row['站名']

                            if pd.notna(lon_label) and pd.notna(lat_label) and pd.notna(rain_value):
                                # 添加蓝色散点标记
                                ax.scatter(lon_label, lat_label, s=10, color='blue', zorder=11, transform=ccrs.PlateCarree())
                                # 添加文字标注
                                label_text = f"{station_name}\n{rain_value:.1f}"
                                ax.text(lon_label, lat_label, label_text,
                                        transform=ccrs.PlateCarree(),
                                        fontsize=7,
                                        color='black',
                                        ha='center',
                                        va='bottom',
                                        zorder=10)
                        except ValueError:
                            # 如果一个组的总降雨量全是 NaN，idxmax() 会引发 ValueError
                            pass # 静默处理
            else:
                print("提示：在其他区县中未找到 '区域内站点' 为 '是' 的站点，不进行额外标注。")

        else:
            missing_cols = [col for col in required_cols_other if col not in other_data.columns]
            print(f"警告：数据中缺少必要的列用于标注其他区县站点: {missing_cols}，无法进行标注。")

    except KeyError as e:
        print(f"警告：处理其他区县数据时发生键错误 '{e}'，请检查列名是否正确。无法标注。")
    except Exception as e:
        print(f"标注其他区县最大降雨量站点时发生未知错误: {e}")
    # --- 标注结束 ---

    # 添加色标
    cbar = plt.colorbar(contour, ax=ax, orientation='horizontal',
                      pad=0.08, fraction=0.05, aspect=40)
    cbar.set_label('降水量 (mm)') # 移除 f-string

    # 如果有高程数据，添加高程信息到标题
    if elevation_masked is not None:
        elevation_range = f"高程范围: {np.min(elevation_masked):.0f}m - {np.max(elevation_masked):.0f}m"
        title = f'平谷区地形与降雨量分布图\n{elevation_range}'
    else:
        title = '平谷降雨量分布图'
    
    # 设置标题
    plt.title(title, fontsize=16, color='red')

    # 添加地理范围信息
    info_text = f'地理范围：\n经度：{x_min:.4f}°E - {x_max:.4f}°E\n纬度：{y_min:.4f}°N - {y_max:.4f}°N'
    plt.figtext(0.02, 0.02, info_text, fontproperties='SimHei',
                fontsize=10, ha='left', va='bottom',
                bbox=dict(facecolor='white', edgecolor='black',
                        alpha=0.8, pad=5))

    # 保存图片
    plt.tight_layout()
    
    try:
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=300)
        plt.close(fig) # 关闭图形，释放内存
        buf.seek(0) # 将指针移到 BytesIO 对象的开头
        return buf # 返回包含图片数据的 BytesIO 对象
    except Exception as e:
        print(f"保存图像到内存时出错: {e}")
        plt.close(fig) # 确保即使保存失败也关闭图形
        return None # 保存失败则返回 None

# 添加主程序入口
if __name__ == "__main__":
    csv_file = 'raw_data.csv'
    dem_file = '高程/target_region_dem.tif'  # 默认使用目标区域的DEM
    
    # 检查DEM文件是否存在
    if not os.path.exists(dem_file):
        print(f"警告: DEM文件 '{dem_file}' 不存在，尝试使用备用文件...")
        # 尝试使用合并的DEM文件
        dem_file = '高程/merged_dem.tif'
        if not os.path.exists(dem_file):
            print(f"警告: 备用DEM文件也不存在，将继续但不显示高程数据。")
            dem_file = None
    
    # 检查 CSV 文件是否存在
    if os.path.exists(csv_file):
        try:
            # 读取 CSV 数据，指定 gbk 编码
            rain_df = pd.read_csv(csv_file, encoding='gbk')
            # 数据清洗：尝试将经纬度和降雨量转换为数值类型，无效值转为 NaN
            rain_df['经度'] = pd.to_numeric(rain_df['经度'], errors='coerce')
            rain_df['纬度'] = pd.to_numeric(rain_df['纬度'], errors='coerce')
            rain_df['总降雨量'] = pd.to_numeric(rain_df['总降雨量'], errors='coerce')

            # 检查必要的列是否存在
            required_cols = ['经度', '纬度', '总降雨量']
            if all(col in rain_df.columns for col in required_cols):
                 # 调用绘图函数，传入高程数据文件
                generate_rainfall_map(rain_df, dem_file)
            else:
                print(f"错误：CSV 文件 '{csv_file}' 缺少必要的列: {required_cols}")

        except FileNotFoundError:
            print(f"错误: 文件 '{csv_file}' 未找到。")
        except UnicodeDecodeError:
            print(f"错误: 无法使用 'gbk' 编码解码文件 '{csv_file}'。请检查文件编码。")
        except Exception as e:
            print(f"处理 CSV 文件或生成地图时发生错误: {e}")
    else:
        print(f"错误: CSV 数据文件 '{csv_file}' 不存在。")