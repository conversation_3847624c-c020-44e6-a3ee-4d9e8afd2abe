#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象数据验证和统计工具
用于检查数据质量、生成统计报告和数据完整性验证
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import logging
from database.database import get_db_connection

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WeatherDataValidator:
    def __init__(self):
        self.conn = get_db_connection()
    
    def __del__(self):
        if hasattr(self, 'conn'):
            self.conn.close()
    
    def check_data_completeness(self, station_id=None, year=None, month=None):
        """检查数据完整性"""
        logger.info("开始检查数据完整性...")
        
        conditions = []
        params = []
        
        if station_id:
            conditions.append("station_id = ?")
            params.append(station_id)
        if year:
            conditions.append("year = ?")
            params.append(year)
        if month:
            conditions.append("month = ?")
            params.append(month)
        
        where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
        
        # 检查各要素的数据量
        tables = [
            'hourly_pressure', 'daily_pressure',
            'hourly_temperature', 'daily_temperature',
            'hourly_humidity', 'daily_humidity',
            'hourly_precipitation', 'daily_precipitation',
            'hourly_wind', 'daily_wind'
        ]
        
        results = {}
        for table in tables:
            query = f"SELECT COUNT(*) FROM {table}{where_clause}"
            cursor = self.conn.execute(query, params)
            count = cursor.fetchone()[0]
            results[table] = count
            logger.info(f"{table}: {count} 条记录")
        
        return results
    
    def check_data_quality(self, station_id=None):
        """检查数据质量问题"""
        logger.info("开始检查数据质量...")
        
        issues = []
        
        # 检查气温异常值
        query = """
        SELECT station_id, year, month, day, hour, temperature 
        FROM hourly_temperature 
        WHERE temperature < -50 OR temperature > 60
        """
        if station_id:
            query += " AND station_id = ?"
            cursor = self.conn.execute(query, (station_id,))
        else:
            cursor = self.conn.execute(query)
        
        temp_issues = cursor.fetchall()
        if temp_issues:
            issues.append(f"发现 {len(temp_issues)} 个异常气温值")
        
        # 检查气压异常值
        query = """
        SELECT station_id, year, month, day, hour, pressure 
        FROM hourly_pressure 
        WHERE pressure < 800 OR pressure > 1100
        """
        if station_id:
            query += " AND station_id = ?"
            cursor = self.conn.execute(query, (station_id,))
        else:
            cursor = self.conn.execute(query)
        
        pressure_issues = cursor.fetchall()
        if pressure_issues:
            issues.append(f"发现 {len(pressure_issues)} 个异常气压值")
        
        # 检查湿度异常值
        query = """
        SELECT station_id, year, month, day, hour, humidity 
        FROM hourly_humidity 
        WHERE humidity < 0 OR humidity > 100
        """
        if station_id:
            query += " AND station_id = ?"
            cursor = self.conn.execute(query, (station_id,))
        else:
            cursor = self.conn.execute(query)
        
        humidity_issues = cursor.fetchall()
        if humidity_issues:
            issues.append(f"发现 {len(humidity_issues)} 个异常湿度值")
        
        return issues
    
    def generate_monthly_statistics(self, station_id, year, month):
        """生成月度统计报告"""
        logger.info(f"生成 {station_id} {year}-{month:02d} 月度统计报告...")
        
        stats = {}
        
        # 气温统计
        query = """
        SELECT AVG(temperature) as avg_temp, MIN(temperature) as min_temp, MAX(temperature) as max_temp
        FROM hourly_temperature 
        WHERE station_id = ? AND year = ? AND month = ?
        """
        cursor = self.conn.execute(query, (station_id, year, month))
        temp_stats = cursor.fetchone()
        if temp_stats and temp_stats[0] is not None:
            stats['temperature'] = {
                'average': round(temp_stats[0], 2),
                'minimum': temp_stats[1],
                'maximum': temp_stats[2]
            }
        
        # 气压统计
        query = """
        SELECT AVG(pressure) as avg_pressure, MIN(pressure) as min_pressure, MAX(pressure) as max_pressure
        FROM hourly_pressure 
        WHERE station_id = ? AND year = ? AND month = ?
        """
        cursor = self.conn.execute(query, (station_id, year, month))
        pressure_stats = cursor.fetchone()
        if pressure_stats and pressure_stats[0] is not None:
            stats['pressure'] = {
                'average': round(pressure_stats[0], 2),
                'minimum': pressure_stats[1],
                'maximum': pressure_stats[2]
            }
        
        # 湿度统计
        query = """
        SELECT AVG(humidity) as avg_humidity, MIN(humidity) as min_humidity, MAX(humidity) as max_humidity
        FROM hourly_humidity 
        WHERE station_id = ? AND year = ? AND month = ?
        """
        cursor = self.conn.execute(query, (station_id, year, month))
        humidity_stats = cursor.fetchone()
        if humidity_stats and humidity_stats[0] is not None:
            stats['humidity'] = {
                'average': round(humidity_stats[0], 2),
                'minimum': humidity_stats[1],
                'maximum': humidity_stats[2]
            }
        
        # 降水统计
        query = """
        SELECT SUM(total_precipitation) as total_precip, AVG(total_precipitation) as avg_precip, MAX(total_precipitation) as max_precip
        FROM daily_precipitation 
        WHERE station_id = ? AND year = ? AND month = ?
        """
        cursor = self.conn.execute(query, (station_id, year, month))
        precip_stats = cursor.fetchone()
        if precip_stats and precip_stats[0] is not None:
            stats['precipitation'] = {
                'total': precip_stats[0],
                'average_daily': round(precip_stats[1], 2) if precip_stats[1] else 0,
                'maximum_daily': precip_stats[2]
            }
        
        return stats
    
    def get_station_list(self):
        """获取所有站点列表"""
        query = "SELECT DISTINCT station_id FROM hourly_temperature ORDER BY station_id"
        cursor = self.conn.execute(query)
        stations = [row[0] for row in cursor.fetchall()]
        return stations
    
    def get_data_time_range(self, station_id=None):
        """获取数据时间范围"""
        if station_id:
            query = """
            SELECT MIN(year) as min_year, MAX(year) as max_year, 
                   MIN(month) as min_month, MAX(month) as max_month
            FROM hourly_temperature 
            WHERE station_id = ?
            """
            cursor = self.conn.execute(query, (station_id,))
        else:
            query = """
            SELECT MIN(year) as min_year, MAX(year) as max_year, 
                   MIN(month) as min_month, MAX(month) as max_month
            FROM hourly_temperature
            """
            cursor = self.conn.execute(query)
        
        result = cursor.fetchone()
        return result

def main():
    """主函数 - 演示数据验证功能"""
    validator = WeatherDataValidator()
    
    # 获取站点列表
    stations = validator.get_station_list()
    print(f"数据库中共有 {len(stations)} 个站点: {stations}")
    
    if stations:
        # 检查第一个站点的数据完整性
        station_id = stations[0]
        print(f"\n检查站点 {station_id} 的数据完整性:")
        completeness = validator.check_data_completeness(station_id=station_id)
        for table, count in completeness.items():
            print(f"  {table}: {count} 条记录")
        
        # 检查数据质量
        print(f"\n检查站点 {station_id} 的数据质量:")
        quality_issues = validator.check_data_quality(station_id=station_id)
        if quality_issues:
            for issue in quality_issues:
                print(f"  ⚠️  {issue}")
        else:
            print("  ✅ 未发现数据质量问题")
        
        # 获取数据时间范围
        time_range = validator.get_data_time_range(station_id=station_id)
        if time_range and time_range[0]:
            print(f"\n站点 {station_id} 数据时间范围: {time_range[0]}-{time_range[2]:02d} 到 {time_range[1]}-{time_range[3]:02d}")
            
            # 生成月度统计
            stats = validator.generate_monthly_statistics(station_id, time_range[1], time_range[3])
            print(f"\n{station_id} {time_range[1]}-{time_range[3]:02d} 月度统计:")
            for element, data in stats.items():
                print(f"  {element}: {data}")

if __name__ == '__main__':
    main()
