import re
import os
import glob
import logging
from datetime import datetime
from database.database import get_db_connection, create_tables
from parsers import pressure, temperature, humidity, precipitation, wind, wet_bulb_temp, vapor_pressure, visibility, sunshine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('weather_data_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def process_a_file(file_path):
    """处理单个A文件"""
    logger.info(f"开始处理文件: {file_path}")

    try:
        with open(file_path, 'r', encoding='gbk') as f:
            content = f.read()
    except Exception as e:
        logger.error(f"读取文件失败 {file_path}: {e}")
        return False

    try:
        # 提取台站参数
        header = content.split('\n', 1)[0]
        parts = header.split()
        if len(parts) < 12:
            logger.error(f"文件头格式不正确: {file_path}")
            return False

        station_id = parts[0]
        year = int(parts[10])
        month = int(parts[11])

        logger.info(f"处理站点: {station_id}, 年月: {year}-{month:02d}")
    except Exception as e:
        logger.error(f"解析文件头失败 {file_path}: {e}")
        return False

    # 1. 将文件按主要部分分割
    try:
        observation_data_full = content.split('\n', 1)[1].split('??????')[0]
    except IndexError:
        logger.error(f"无法找到观测数据部分: {file_path}")
        return False

    # 2. 将观测数据分割成每个要素的块
    # 修正正则表达式，确保每个要素的数据块是独立的
    # 先统一换行符
    observation_data_full = observation_data_full.replace('\r\n', '\n').replace('\r', '\n')
    element_blocks = re.findall(r'([A-Z])([A-Z0-9])\n(.*?)(?=\n[A-Z][A-Z0-9]|\n\?{6}|\n\*{6}|\n#{6}|$)', observation_data_full, re.DOTALL)

    logger.info(f"提取到 {len(element_blocks)} 个气象要素块")
    for block in element_blocks:
        logger.debug(f"要素代码: {block[0]}, 方式位: {block[1]}, 数据长度: {len(block[2])}")

    try:
        conn = get_db_connection()
        cursor = conn.cursor()
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False

    processed_elements = 0
    for element_code, mode, data in element_blocks:
        try:
            # Replace all newline characters within data with spaces
            data = data.replace('\r\n', ' ').replace('\n', ' ')

            context = {
                "station_id": station_id,
                "year": year,
                "month": month,
                "mode": mode,
                "data": data
            }

            if element_code == 'P':
                hourly, daily = pressure.parse_pressure(context)
                for item in hourly:
                    cursor.execute("INSERT OR IGNORE INTO hourly_pressure (station_id, year, month, day, hour, pressure) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['hour'], item['pressure']))
                for item in daily:
                    cursor.execute("INSERT OR IGNORE INTO daily_pressure (station_id, year, month, day, max_pressure, min_pressure) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['max_pressure'], item['min_pressure']))
                logger.debug(f"气压数据: {len(hourly)} 小时记录, {len(daily)} 日记录")
            elif element_code == 'T':
                hourly, daily = temperature.parse_temperature(context)
                for item in hourly:
                    cursor.execute("INSERT OR IGNORE INTO hourly_temperature (station_id, year, month, day, hour, temperature) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['hour'], item['temperature']))
                for item in daily:
                    cursor.execute("INSERT OR IGNORE INTO daily_temperature (station_id, year, month, day, max_temperature, min_temperature) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['max_temperature'], item['min_temperature']))
                logger.debug(f"气温数据: {len(hourly)} 小时记录, {len(daily)} 日记录")
            elif element_code == 'U':
                hourly, daily = humidity.parse_humidity(context)
                for item in hourly:
                    cursor.execute("INSERT OR IGNORE INTO hourly_humidity (station_id, year, month, day, hour, humidity) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['hour'], item['humidity']))
                for item in daily:
                    cursor.execute("INSERT OR IGNORE INTO daily_humidity (station_id, year, month, day, min_humidity) VALUES (?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['min_humidity']))
                logger.debug(f"湿度数据: {len(hourly)} 小时记录, {len(daily)} 日记录")
            elif element_code == 'R':
                hourly, daily = precipitation.parse_precipitation(context)
                for item in hourly:
                    cursor.execute("INSERT OR IGNORE INTO hourly_precipitation (station_id, year, month, day, hour, precipitation) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['hour'], item['precipitation']))
                for item in daily:
                    cursor.execute("INSERT OR IGNORE INTO daily_precipitation (station_id, year, month, day, total_precipitation) VALUES (?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['total_precipitation']))
                logger.debug(f"降水数据: {len(hourly)} 小时记录, {len(daily)} 日记录")
            elif element_code == 'F':
                hourly, daily = wind.parse_wind(context)
                for item in hourly:
                    cursor.execute("INSERT OR IGNORE INTO hourly_wind (station_id, year, month, day, hour, wind_direction, wind_speed) VALUES (?, ?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['hour'], item['wind_direction'], item['wind_speed']))
                for item in daily:
                    cursor.execute("INSERT OR IGNORE INTO daily_wind (station_id, year, month, day, max_wind_speed, extreme_wind_speed) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['max_wind_speed'], item['extreme_wind_speed']))
                logger.debug(f"风数据: {len(hourly)} 小时记录, {len(daily)} 日记录")
            elif element_code == 'I': # 湿球温度
                hourly, daily = wet_bulb_temp.parse_wet_bulb_temp(context)
                for item in hourly:
                    cursor.execute("INSERT OR IGNORE INTO hourly_wet_bulb_temperature (station_id, year, month, day, hour, wet_bulb_temperature) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['hour'], item['wet_bulb_temperature']))
                for item in daily:
                    cursor.execute("INSERT OR IGNORE INTO daily_wet_bulb_temperature (station_id, year, month, day, wet_bulb_temperature) VALUES (?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['wet_bulb_temperature']))
                logger.debug(f"湿球温度数据: {len(hourly)} 小时记录, {len(daily)} 日记录")
            elif element_code == 'E': # 水汽压
                hourly, daily = vapor_pressure.parse_vapor_pressure(context)
                for item in hourly:
                    cursor.execute("INSERT OR IGNORE INTO hourly_vapor_pressure (station_id, year, month, day, hour, vapor_pressure) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['hour'], item['vapor_pressure']))
                for item in daily:
                    cursor.execute("INSERT OR IGNORE INTO daily_vapor_pressure (station_id, year, month, day, vapor_pressure) VALUES (?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['vapor_pressure']))
                logger.debug(f"水汽压数据: {len(hourly)} 小时记录, {len(daily)} 日记录")
            elif element_code == 'V': # 能见度
                hourly, daily = visibility.parse_visibility(context)
                for item in hourly:
                    cursor.execute("INSERT OR IGNORE INTO hourly_visibility (station_id, year, month, day, hour, visibility) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['hour'], item['visibility']))
                for item in daily:
                    cursor.execute("INSERT OR IGNORE INTO daily_visibility (station_id, year, month, day, min_visibility) VALUES (?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['min_visibility']))
                logger.debug(f"能见度数据: {len(hourly)} 小时记录, {len(daily)} 日记录")
            elif element_code == 'S': # 日照时数
                hourly, daily = sunshine.parse_sunshine(context)
                for item in hourly:
                    cursor.execute("INSERT OR IGNORE INTO hourly_sunshine_hours (station_id, year, month, day, hour, sunshine_hours) VALUES (?, ?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['hour'], item['sunshine_hours']))
                for item in daily:
                    cursor.execute("INSERT OR IGNORE INTO daily_sunshine_hours (station_id, year, month, day, total_sunshine_hours) VALUES (?, ?, ?, ?, ?)",
                                   (item['station_id'], item['year'], item['month'], item['day'], item['total_sunshine_hours']))
                logger.debug(f"日照数据: {len(hourly)} 小时记录, {len(daily)} 日记录")

            processed_elements += 1

        except Exception as e:
            logger.error(f"处理要素 {element_code} 时出错: {e}")
            continue

    try:
        conn.commit()
        conn.close()
        logger.info(f"文件处理完成: {file_path}, 处理了 {processed_elements} 个要素")
        return True
    except Exception as e:
        logger.error(f"数据库提交失败: {e}")
        return False

def process_multiple_files(file_pattern="A*.TXT"):
    """批量处理多个A文件"""
    files = glob.glob(file_pattern)
    if not files:
        logger.warning(f"未找到匹配的文件: {file_pattern}")
        return

    logger.info(f"找到 {len(files)} 个文件待处理")

    success_count = 0
    for file_path in files:
        if process_a_file(file_path):
            success_count += 1

    logger.info(f"批量处理完成: 成功 {success_count}/{len(files)} 个文件")

if __name__ == '__main__':
    create_tables()

    # 处理单个文件或批量处理
    import sys
    if len(sys.argv) > 1:
        file_pattern = sys.argv[1]
        if os.path.isfile(file_pattern):
            process_a_file(file_pattern)
        else:
            process_multiple_files(file_pattern)
    else:
        # 默认处理当前目录下所有A文件
        process_multiple_files()

    print("数据处理完成。")