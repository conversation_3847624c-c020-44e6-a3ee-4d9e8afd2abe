# 气象数据处理系统

这是一个基于Python的气象数据处理系统，专门用于处理符合中国气象局A文件格式标准的地面气象观测数据。系统能够将A文件中的气象数据解析并存储到SQLite数据库中，支持小时数据和日数据的分别存储。

## 功能特性

- **A文件解析**: 支持解析符合A文件格式标准的气象数据文件
- **数据库存储**: 自动创建数据库表结构，分别存储小时数据和日数据
- **多要素支持**: 支持气压、气温、湿度、降水、风等多种气象要素
- **批量处理**: 支持单文件和批量文件处理
- **数据验证**: 提供数据质量检查和完整性验证功能
- **数据导出**: 支持将数据导出为CSV、Excel等格式
- **日志记录**: 详细的处理日志，便于问题排查

## 系统架构

```
geminiA/
├── main.py                 # 主程序，处理A文件
├── data_validator.py       # 数据验证工具
├── data_exporter.py        # 数据导出工具
├── database/
│   └── database.py         # 数据库操作模块
├── parsers/                # 各气象要素解析器
│   ├── pressure.py         # 气压解析器
│   ├── temperature.py      # 气温解析器
│   ├── humidity.py         # 湿度解析器
│   ├── precipitation.py    # 降水解析器
│   ├── wind.py            # 风解析器
│   └── ...                # 其他要素解析器
├── weather_data.db         # SQLite数据库文件
└── README.md              # 使用说明
```

## 数据库结构

系统为每个气象要素创建了小时数据表和日数据表：

### 小时数据表
- `hourly_pressure`: 小时气压数据
- `hourly_temperature`: 小时气温数据
- `hourly_humidity`: 小时湿度数据
- `hourly_precipitation`: 小时降水数据
- `hourly_wind`: 小时风数据

### 日数据表
- `daily_pressure`: 日气压极值数据
- `daily_temperature`: 日气温极值数据
- `daily_humidity`: 日湿度极值数据
- `daily_precipitation`: 日降水总量数据
- `daily_wind`: 日风极值数据

## 安装和使用

### 环境要求
- Python 3.7+
- pandas
- openpyxl (用于Excel导出)

### 安装依赖
```bash
pip install pandas openpyxl
```

### 基本使用

#### 1. 处理单个A文件
```bash
python main.py A54424-202207.TXT
```

#### 2. 批量处理A文件
```bash
# 处理当前目录下所有A文件
python main.py

# 处理指定模式的文件
python main.py "A*.TXT"
```

#### 3. 数据验证
```bash
# 运行数据验证工具
python data_validator.py
```

#### 4. 数据导出
```bash
# 导出指定站点的气温和气压数据
python data_exporter.py --station 54424 --elements temperature pressure --format csv

# 导出指定时间范围的数据
python data_exporter.py --station 54424 --start-date 2022-07-01 --end-date 2022-07-31 --format excel

# 查看可用站点
python data_exporter.py --list-stations

# 查看站点数据概要
python data_exporter.py --station 54424 --summary
```

## A文件格式说明

A文件是中国气象局制定的地面气象观测数据文件格式，包含以下主要部分：

1. **台站参数**: 包含站点信息、地理位置、观测设备等
2. **观测数据**: 包含20个气象要素的观测数据
3. **质量控制**: 数据质量控制信息
4. **附加信息**: 月报封面、纪要等

### 支持的气象要素

| 要素代码 | 要素名称 | 说明 |
|---------|---------|------|
| P | 气压 | 本站气压和海平面气压 |
| T | 气温 | 干球温度 |
| I | 湿球温度 | 湿球温度和露点温度 |
| E | 水汽压 | 水汽压 |
| U | 相对湿度 | 相对湿度 |
| N | 云量 | 总云量和低云量 |
| H | 云高 | 云底高度 |
| C | 云状 | 云状 |
| V | 能见度 | 水平能见度 |
| R | 降水量 | 降水量 |
| W | 天气现象 | 天气现象编码 |
| F | 风 | 风向风速 |
| S | 日照时数 | 日照时数 |

## 数据质量控制

系统提供多层次的数据质量控制：

1. **格式验证**: 检查数据格式是否符合A文件标准
2. **范围检查**: 检查数据值是否在合理范围内
3. **完整性检查**: 检查数据的完整性和连续性
4. **重复数据处理**: 自动处理重复数据

## 日志和错误处理

- 系统会生成详细的处理日志，保存在 `weather_data_processing.log` 文件中
- 对于解析错误的数据，系统会记录错误信息但继续处理其他数据
- 支持断点续传，重复运行不会产生重复数据

## 性能优化

- 使用数据库索引提高查询性能
- 批量插入数据减少数据库操作次数
- 内存优化处理大文件

## 扩展功能

系统设计为模块化架构，可以轻松扩展：

1. **新增气象要素**: 在 `parsers/` 目录下添加新的解析器
2. **新增数据格式**: 修改主程序支持其他数据格式
3. **新增导出格式**: 在导出工具中添加新的导出格式
4. **新增数据库**: 支持其他数据库系统

## 故障排除

### 常见问题

1. **编码问题**: A文件通常使用GBK编码，系统已自动处理
2. **内存不足**: 处理大文件时可能需要增加系统内存
3. **数据库锁定**: 多进程同时访问数据库时可能出现锁定

### 调试模式

设置日志级别为DEBUG可以获得更详细的调试信息：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 贡献

欢迎提交问题报告和功能请求。如需贡献代码，请：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 本系统专门针对中国气象局A文件格式设计，使用前请确保数据文件符合相应标准。
