#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象数据查询和导出工具
支持按条件查询数据并导出为CSV、Excel等格式
"""

import sqlite3
import pandas as pd
import argparse
from datetime import datetime
import logging
from database.database import get_db_connection

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WeatherDataExporter:
    def __init__(self):
        self.conn = get_db_connection()
    
    def __del__(self):
        if hasattr(self, 'conn'):
            self.conn.close()
    
    def query_hourly_data(self, station_id, start_date=None, end_date=None, elements=None):
        """
        查询小时数据
        
        Args:
            station_id: 站点ID
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            elements: 要素列表 ['temperature', 'pressure', 'humidity', 'precipitation', 'wind']
        """
        if elements is None:
            elements = ['temperature', 'pressure', 'humidity']
        
        # 构建查询条件
        conditions = [f"station_id = '{station_id}'"]
        
        if start_date:
            start_parts = start_date.split('-')
            conditions.append(f"(year > {start_parts[0]} OR (year = {start_parts[0]} AND month > {start_parts[1]}) OR (year = {start_parts[0]} AND month = {start_parts[1]} AND day >= {start_parts[2]}))")
        
        if end_date:
            end_parts = end_date.split('-')
            conditions.append(f"(year < {end_parts[0]} OR (year = {end_parts[0]} AND month < {end_parts[1]}) OR (year = {end_parts[0]} AND month = {end_parts[1]} AND day <= {end_parts[2]}))")
        
        where_clause = " AND ".join(conditions)
        
        # 构建查询语句
        queries = []
        
        if 'temperature' in elements:
            query = f"""
            SELECT station_id, year, month, day, hour, temperature, 'temperature' as element
            FROM hourly_temperature 
            WHERE {where_clause}
            """
            queries.append(query)
        
        if 'pressure' in elements:
            query = f"""
            SELECT station_id, year, month, day, hour, pressure, 'pressure' as element
            FROM hourly_pressure 
            WHERE {where_clause}
            """
            queries.append(query)
        
        if 'humidity' in elements:
            query = f"""
            SELECT station_id, year, month, day, hour, humidity, 'humidity' as element
            FROM hourly_humidity 
            WHERE {where_clause}
            """
            queries.append(query)
        
        if 'precipitation' in elements:
            query = f"""
            SELECT station_id, year, month, day, hour, precipitation, 'precipitation' as element
            FROM hourly_precipitation 
            WHERE {where_clause}
            """
            queries.append(query)
        
        if 'wind' in elements:
            query = f"""
            SELECT station_id, year, month, day, hour, 
                   CAST(wind_speed as REAL) as wind_speed, 'wind_speed' as element
            FROM hourly_wind 
            WHERE {where_clause}
            UNION ALL
            SELECT station_id, year, month, day, hour, 
                   wind_direction, 'wind_direction' as element
            FROM hourly_wind 
            WHERE {where_clause}
            """
            queries.append(query)
        
        # 执行查询
        all_data = []
        for query in queries:
            df = pd.read_sql_query(query, self.conn)
            all_data.append(df)
        
        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            # 添加日期时间列
            result['datetime'] = pd.to_datetime(result[['year', 'month', 'day', 'hour']])
            return result
        else:
            return pd.DataFrame()
    
    def query_daily_data(self, station_id, start_date=None, end_date=None, elements=None):
        """查询日数据"""
        if elements is None:
            elements = ['temperature', 'pressure', 'humidity', 'precipitation']
        
        # 构建查询条件
        conditions = [f"station_id = '{station_id}'"]
        
        if start_date:
            start_parts = start_date.split('-')
            conditions.append(f"(year > {start_parts[0]} OR (year = {start_parts[0]} AND month > {start_parts[1]}) OR (year = {start_parts[0]} AND month = {start_parts[1]} AND day >= {start_parts[2]}))")
        
        if end_date:
            end_parts = end_date.split('-')
            conditions.append(f"(year < {end_parts[0]} OR (year = {end_parts[0]} AND month < {end_parts[1]}) OR (year = {end_parts[0]} AND month = {end_parts[1]} AND day <= {end_parts[2]}))")
        
        where_clause = " AND ".join(conditions)
        
        # 构建联合查询
        queries = []
        
        if 'temperature' in elements:
            queries.append(f"""
            SELECT station_id, year, month, day, 
                   max_temperature, min_temperature, 
                   'temperature' as element_type
            FROM daily_temperature 
            WHERE {where_clause}
            """)
        
        if 'pressure' in elements:
            queries.append(f"""
            SELECT station_id, year, month, day, 
                   max_pressure as max_value, min_pressure as min_value, 
                   'pressure' as element_type
            FROM daily_pressure 
            WHERE {where_clause}
            """)
        
        if 'humidity' in elements:
            queries.append(f"""
            SELECT station_id, year, month, day, 
                   NULL as max_value, min_humidity as min_value, 
                   'humidity' as element_type
            FROM daily_humidity 
            WHERE {where_clause}
            """)
        
        if 'precipitation' in elements:
            queries.append(f"""
            SELECT station_id, year, month, day, 
                   total_precipitation as total_value, NULL as min_value, 
                   'precipitation' as element_type
            FROM daily_precipitation 
            WHERE {where_clause}
            """)
        
        if queries:
            # 标准化列名
            standardized_queries = []
            for i, query in enumerate(queries):
                if 'temperature' in query:
                    standardized_queries.append(query)
                elif 'precipitation' in query:
                    standardized_queries.append(query.replace('total_value', 'max_value'))
                else:
                    standardized_queries.append(query.replace('max_value', 'max_temperature').replace('min_value', 'min_temperature'))
            
            union_query = " UNION ALL ".join(standardized_queries)
            df = pd.read_sql_query(union_query, self.conn)
            
            # 添加日期列
            df['date'] = pd.to_datetime(df[['year', 'month', 'day']])
            return df
        else:
            return pd.DataFrame()
    
    def export_to_csv(self, data, filename):
        """导出数据到CSV文件"""
        try:
            data.to_csv(filename, index=False, encoding='utf-8-sig')
            logger.info(f"数据已导出到: {filename}")
            return True
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return False
    
    def export_to_excel(self, data, filename, sheet_name='WeatherData'):
        """导出数据到Excel文件"""
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                data.to_excel(writer, sheet_name=sheet_name, index=False)
            logger.info(f"数据已导出到: {filename}")
            return True
        except Exception as e:
            logger.error(f"导出Excel失败: {e}")
            return False
    
    def get_available_stations(self):
        """获取可用的站点列表"""
        query = "SELECT DISTINCT station_id FROM hourly_temperature ORDER BY station_id"
        df = pd.read_sql_query(query, self.conn)
        return df['station_id'].tolist()
    
    def get_data_summary(self, station_id):
        """获取指定站点的数据概要"""
        summary = {}
        
        # 获取数据时间范围
        query = f"""
        SELECT MIN(year) as min_year, MAX(year) as max_year,
               MIN(month) as min_month, MAX(month) as max_month,
               MIN(day) as min_day, MAX(day) as max_day,
               COUNT(*) as total_records
        FROM hourly_temperature 
        WHERE station_id = '{station_id}'
        """
        
        df = pd.read_sql_query(query, self.conn)
        if not df.empty:
            row = df.iloc[0]
            summary['time_range'] = {
                'start': f"{row['min_year']}-{row['min_month']:02d}-{row['min_day']:02d}",
                'end': f"{row['max_year']}-{row['max_month']:02d}-{row['max_day']:02d}",
                'total_records': row['total_records']
            }
        
        # 获取各要素的记录数
        tables = ['hourly_temperature', 'hourly_pressure', 'hourly_humidity', 
                 'hourly_precipitation', 'hourly_wind']
        
        for table in tables:
            query = f"SELECT COUNT(*) as count FROM {table} WHERE station_id = '{station_id}'"
            df = pd.read_sql_query(query, self.conn)
            element_name = table.replace('hourly_', '')
            summary[element_name] = df.iloc[0]['count'] if not df.empty else 0
        
        return summary

def main():
    """命令行接口"""
    parser = argparse.ArgumentParser(description='气象数据查询和导出工具')
    parser.add_argument('--station', required=True, help='站点ID')
    parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--elements', nargs='+', 
                       choices=['temperature', 'pressure', 'humidity', 'precipitation', 'wind'],
                       default=['temperature', 'pressure', 'humidity'],
                       help='要导出的气象要素')
    parser.add_argument('--type', choices=['hourly', 'daily'], default='hourly',
                       help='数据类型')
    parser.add_argument('--format', choices=['csv', 'excel'], default='csv',
                       help='导出格式')
    parser.add_argument('--output', help='输出文件名')
    parser.add_argument('--list-stations', action='store_true', help='列出所有可用站点')
    parser.add_argument('--summary', action='store_true', help='显示站点数据概要')
    
    args = parser.parse_args()
    
    exporter = WeatherDataExporter()
    
    if args.list_stations:
        stations = exporter.get_available_stations()
        print(f"可用站点 ({len(stations)} 个):")
        for station in stations:
            print(f"  {station}")
        return
    
    if args.summary:
        summary = exporter.get_data_summary(args.station)
        print(f"站点 {args.station} 数据概要:")
        for key, value in summary.items():
            print(f"  {key}: {value}")
        return
    
    # 查询数据
    if args.type == 'hourly':
        data = exporter.query_hourly_data(args.station, args.start_date, args.end_date, args.elements)
    else:
        data = exporter.query_daily_data(args.station, args.start_date, args.end_date, args.elements)
    
    if data.empty:
        print("未找到匹配的数据")
        return
    
    print(f"查询到 {len(data)} 条记录")
    
    # 生成输出文件名
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"{args.station}_{args.type}_{timestamp}.{args.format}"
    
    # 导出数据
    if args.format == 'csv':
        success = exporter.export_to_csv(data, args.output)
    else:
        success = exporter.export_to_excel(data, args.output)
    
    if success:
        print(f"数据已成功导出到: {args.output}")
    else:
        print("数据导出失败")

if __name__ == '__main__':
    main()
