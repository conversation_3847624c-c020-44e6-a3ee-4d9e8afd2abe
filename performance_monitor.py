#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象数据处理系统性能监控工具
监控系统性能指标，包括处理速度、内存使用、数据库性能等
"""

import os
import sys
import time
import psutil
import sqlite3
from datetime import datetime, timedelta
import logging
from database.database import get_db_connection

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    def __init__(self):
        self.start_time = None
        self.process = psutil.Process()
        self.initial_memory = None
        self.stats = {
            'files_processed': 0,
            'records_inserted': 0,
            'errors_encountered': 0,
            'processing_times': [],
            'memory_usage': [],
            'cpu_usage': []
        }
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        logger.info("性能监控开始")
    
    def record_file_processed(self, file_path, processing_time, records_count, errors=0):
        """记录文件处理信息"""
        self.stats['files_processed'] += 1
        self.stats['records_inserted'] += records_count
        self.stats['errors_encountered'] += errors
        self.stats['processing_times'].append(processing_time)
        
        # 记录当前内存和CPU使用情况
        memory_mb = self.process.memory_info().rss / 1024 / 1024
        cpu_percent = self.process.cpu_percent()
        
        self.stats['memory_usage'].append(memory_mb)
        self.stats['cpu_usage'].append(cpu_percent)
        
        logger.info(f"文件处理完成: {file_path}")
        logger.info(f"  处理时间: {processing_time:.2f}秒")
        logger.info(f"  记录数: {records_count}")
        logger.info(f"  内存使用: {memory_mb:.1f}MB")
        logger.info(f"  CPU使用: {cpu_percent:.1f}%")
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 获取各表的记录数
            tables = [
                'hourly_pressure', 'daily_pressure',
                'hourly_temperature', 'daily_temperature',
                'hourly_humidity', 'daily_humidity',
                'hourly_precipitation', 'daily_precipitation',
                'hourly_wind', 'daily_wind'
            ]
            
            table_stats = {}
            total_records = 0
            
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    table_stats[table] = count
                    total_records += count
                except sqlite3.Error as e:
                    logger.warning(f"无法获取表 {table} 的统计信息: {e}")
                    table_stats[table] = 0
            
            # 获取数据库文件大小
            db_file = 'weather_data.db'
            db_size_mb = 0
            if os.path.exists(db_file):
                db_size_mb = os.path.getsize(db_file) / 1024 / 1024
            
            conn.close()
            
            return {
                'total_records': total_records,
                'table_stats': table_stats,
                'database_size_mb': db_size_mb
            }
            
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {e}")
            return None
    
    def get_system_info(self):
        """获取系统信息"""
        try:
            # 系统信息
            cpu_count = psutil.cpu_count()
            memory_total = psutil.virtual_memory().total / 1024 / 1024 / 1024  # GB
            disk_usage = psutil.disk_usage('.').percent
            
            # 当前进程信息
            current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
            current_cpu = self.process.cpu_percent()
            
            return {
                'cpu_cores': cpu_count,
                'total_memory_gb': memory_total,
                'disk_usage_percent': disk_usage,
                'process_memory_mb': current_memory,
                'process_cpu_percent': current_cpu
            }
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return None
    
    def calculate_performance_metrics(self):
        """计算性能指标"""
        if not self.start_time:
            return None
        
        total_time = time.time() - self.start_time
        
        metrics = {
            'total_runtime_seconds': total_time,
            'files_processed': self.stats['files_processed'],
            'total_records': self.stats['records_inserted'],
            'total_errors': self.stats['errors_encountered'],
            'avg_processing_time': 0,
            'records_per_second': 0,
            'files_per_minute': 0,
            'peak_memory_mb': 0,
            'avg_cpu_percent': 0,
            'memory_growth_mb': 0
        }
        
        if self.stats['processing_times']:
            metrics['avg_processing_time'] = sum(self.stats['processing_times']) / len(self.stats['processing_times'])
        
        if total_time > 0:
            metrics['records_per_second'] = self.stats['records_inserted'] / total_time
            metrics['files_per_minute'] = (self.stats['files_processed'] / total_time) * 60
        
        if self.stats['memory_usage']:
            metrics['peak_memory_mb'] = max(self.stats['memory_usage'])
            current_memory = self.stats['memory_usage'][-1]
            metrics['memory_growth_mb'] = current_memory - self.initial_memory if self.initial_memory else 0
        
        if self.stats['cpu_usage']:
            metrics['avg_cpu_percent'] = sum(self.stats['cpu_usage']) / len(self.stats['cpu_usage'])
        
        return metrics
    
    def generate_report(self):
        """生成性能报告"""
        print("\n" + "=" * 70)
        print("                    性能监控报告")
        print("=" * 70)
        
        # 基本统计
        metrics = self.calculate_performance_metrics()
        if metrics:
            print(f"\n📊 处理统计:")
            print(f"  总运行时间: {metrics['total_runtime_seconds']:.2f} 秒")
            print(f"  处理文件数: {metrics['files_processed']}")
            print(f"  插入记录数: {metrics['total_records']}")
            print(f"  错误数量: {metrics['total_errors']}")
            
            print(f"\n⚡ 性能指标:")
            print(f"  平均文件处理时间: {metrics['avg_processing_time']:.2f} 秒")
            print(f"  记录处理速度: {metrics['records_per_second']:.1f} 记录/秒")
            print(f"  文件处理速度: {metrics['files_per_minute']:.1f} 文件/分钟")
            
            print(f"\n💾 内存使用:")
            print(f"  峰值内存使用: {metrics['peak_memory_mb']:.1f} MB")
            print(f"  内存增长: {metrics['memory_growth_mb']:.1f} MB")
            print(f"  平均CPU使用: {metrics['avg_cpu_percent']:.1f}%")
        
        # 数据库统计
        db_stats = self.get_database_stats()
        if db_stats:
            print(f"\n🗄️  数据库统计:")
            print(f"  总记录数: {db_stats['total_records']:,}")
            print(f"  数据库大小: {db_stats['database_size_mb']:.1f} MB")
            print(f"  主要数据表:")
            for table, count in db_stats['table_stats'].items():
                if count > 0:
                    print(f"    {table}: {count:,} 条记录")
        
        # 系统信息
        sys_info = self.get_system_info()
        if sys_info:
            print(f"\n🖥️  系统信息:")
            print(f"  CPU核心数: {sys_info['cpu_cores']}")
            print(f"  总内存: {sys_info['total_memory_gb']:.1f} GB")
            print(f"  磁盘使用率: {sys_info['disk_usage_percent']:.1f}%")
            print(f"  当前进程内存: {sys_info['process_memory_mb']:.1f} MB")
            print(f"  当前进程CPU: {sys_info['process_cpu_percent']:.1f}%")
        
        print("\n" + "=" * 70)
    
    def save_report_to_file(self, filename=None):
        """保存报告到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                # 重定向输出到文件
                import sys
                old_stdout = sys.stdout
                sys.stdout = f
                
                self.generate_report()
                
                # 恢复输出
                sys.stdout = old_stdout
            
            logger.info(f"性能报告已保存到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
            return None
    
    def monitor_database_performance(self):
        """监控数据库性能"""
        try:
            conn = get_db_connection()
            
            # 测试查询性能
            test_queries = [
                "SELECT COUNT(*) FROM hourly_temperature",
                "SELECT station_id, AVG(temperature) FROM hourly_temperature GROUP BY station_id",
                "SELECT * FROM hourly_temperature WHERE year = 2022 AND month = 7 LIMIT 100"
            ]
            
            query_times = []
            for query in test_queries:
                start_time = time.time()
                cursor = conn.cursor()
                cursor.execute(query)
                cursor.fetchall()
                query_time = time.time() - start_time
                query_times.append(query_time)
            
            conn.close()
            
            print(f"\n🔍 数据库查询性能测试:")
            print(f"  简单计数查询: {query_times[0]:.3f} 秒")
            print(f"  分组聚合查询: {query_times[1]:.3f} 秒")
            print(f"  条件查询: {query_times[2]:.3f} 秒")
            
            return query_times
            
        except Exception as e:
            logger.error(f"数据库性能测试失败: {e}")
            return None

def main():
    """主函数 - 演示性能监控功能"""
    monitor = PerformanceMonitor()
    
    print("气象数据处理系统性能监控工具")
    print("=" * 50)
    
    # 开始监控
    monitor.start_monitoring()
    
    # 模拟一些处理过程
    print("正在收集系统信息...")
    time.sleep(1)
    
    # 生成报告
    monitor.generate_report()
    
    # 数据库性能测试
    monitor.monitor_database_performance()
    
    # 保存报告
    report_file = monitor.save_report_to_file()
    if report_file:
        print(f"\n📄 详细报告已保存到: {report_file}")

if __name__ == '__main__':
    main()
