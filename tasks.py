"""
具体的任务实现
"""
from datetime import datetime, timedelta
import requests
import json
from loguru import logger
from flask import current_app
from models import ForecastData, RainfallData, Warning, db
from data_sources import ForecastDataSource, RainfallDataSource, WarningDataSource
from publishers import WechatPublisher, FilePublisher

def forecast_task(config):
    """天气预报任务"""
    try:
        logger.info("开始执行天气预报任务")
        
        # 获取配置
        district = config.get('district', '平谷')
        num_lines = config.get('num_lines', 2)
        
        # 获取预报数据
        data_source = ForecastDataSource()
        forecast_data = data_source.get_forecast_data(district)
        
        if not forecast_data:
            raise Exception("获取预报数据失败")
        
        # 生成预报文本
        forecast_text = data_source.format_forecast_text(district, forecast_data, num_lines)
        
        # 保存到数据库
        forecast_record = ForecastData(
            district=district,
            forecast_date=datetime.now().date(),
            forecast_text=forecast_text
        )
        db.session.add(forecast_record)
        db.session.commit()
        
        # 发布预报
        if config.get('auto_publish', True):
            publisher = WechatPublisher()
            publisher.publish_forecast(forecast_text, config.get('target_groups', []))
        
        logger.info("天气预报任务执行完成")
        return {
            'status': 'success',
            'message': '天气预报任务执行完成',
            'forecast_text': forecast_text
        }
        
    except Exception as e:
        logger.error(f"天气预报任务执行失败: {str(e)}")
        raise

def rainfall_task(config):
    """雨量监测任务"""
    try:
        logger.info("开始执行雨量监测任务")
        
        # 获取当前日期
        target_date = datetime.now()
        
        # 获取雨量数据
        data_source = RainfallDataSource()
        rainfall_result, stations_df = data_source.get_rainfall_data(target_date)
        
        if not rainfall_result:
            raise Exception("获取雨量数据失败")
        
        # 生成雨量报告
        report_text = data_source.generate_rainfall_report(target_date, rainfall_result)
        
        # 保存站点数据到数据库
        for station_name in stations_df.index:
            station_data = stations_df.loc[station_name]
            rainfall_record = RainfallData(
                date=target_date.date(),
                station_name=station_name,
                station_id=station_data.get('stationId'),
                rainfall=float(station_data.get('rain5m', 0))
            )
            db.session.add(rainfall_record)
        
        db.session.commit()
        
        # 发布雨量信息
        if config.get('auto_publish', True) and rainfall_result['average'] >= config.get('min_rainfall', 1.0):
            publisher = WechatPublisher()
            publisher.publish_rainfall(report_text, config.get('target_groups', []))
        
        logger.info("雨量监测任务执行完成")
        return {
            'status': 'success',
            'message': '雨量监测任务执行完成',
            'report_text': report_text,
            'average_rainfall': rainfall_result['average']
        }
        
    except Exception as e:
        logger.error(f"雨量监测任务执行失败: {str(e)}")
        raise

def warning_task(config):
    """预警监测任务"""
    try:
        logger.info("开始执行预警监测任务")
        
        # 获取预警数据
        data_source = WarningDataSource()
        new_warnings = data_source.check_new_warnings()
        
        processed_count = 0
        
        for warning_data in new_warnings:
            try:
                # 处理预警
                warning_dict, should_release = data_source.process_warning(warning_data)
                
                if warning_dict:
                    # 保存到数据库
                    warning_record = Warning(
                        warning_number=str(warning_dict['编号']),
                        publish_time=warning_dict['发布时间'],
                        status=warning_dict['状态'],
                        warning_type=warning_dict['预警类型'],
                        warning_level=warning_dict['预警等级'],
                        content=warning_dict['预警内容'],
                        start_time=warning_dict.get('开始时间'),
                        end_time=warning_dict.get('结束时间'),
                        is_released=should_release
                    )
                    db.session.add(warning_record)
                    
                    # 发布预警
                    if should_release and config.get('auto_publish', True):
                        publisher = WechatPublisher()
                        publisher.publish_warning(warning_dict, config.get('target_groups', []))
                    
                    processed_count += 1
                    
            except Exception as e:
                logger.error(f"处理预警失败: {str(e)}")
        
        db.session.commit()
        
        logger.info(f"预警监测任务执行完成，处理了 {processed_count} 条预警")
        return {
            'status': 'success',
            'message': f'预警监测任务执行完成，处理了 {processed_count} 条预警',
            'processed_count': processed_count
        }
        
    except Exception as e:
        logger.error(f"预警监测任务执行失败: {str(e)}")
        raise

def temperature_task(config):
    """温度查询任务"""
    try:
        logger.info("开始执行温度查询任务")
        
        # 获取温度数据 (简化版本，可以根据需要扩展)
        # 这里假设有一个温度数据源
        from data_sources import TemperatureDataSource
        
        data_source = TemperatureDataSource()
        temp_data = data_source.get_temperature_data()
        
        if not temp_data:
            raise Exception("获取温度数据失败")
        
        # 格式化温度信息
        temp_text = data_source.format_temperature_text(temp_data)
        
        # 发布温度信息
        if config.get('auto_publish', True):
            publisher = WechatPublisher()
            publisher.publish_temperature(temp_text, config.get('target_groups', []))
        
        logger.info("温度查询任务执行完成")
        return {
            'status': 'success',
            'message': '温度查询任务执行完成',
            'temperature_text': temp_text
        }
        
    except Exception as e:
        logger.error(f"温度查询任务执行失败: {str(e)}")
        raise

def download_task(config):
    """文件下载任务"""
    try:
        logger.info("开始执行文件下载任务")
        
        # 这里实现原来的区县联播下载功能
        from data_sources import FileDownloadSource
        
        download_source = FileDownloadSource()
        result = download_source.download_broadcast_file(config)
        
        logger.info("文件下载任务执行完成")
        return {
            'status': 'success',
            'message': '文件下载任务执行完成',
            'downloaded_file': result.get('file_path')
        }
        
    except Exception as e:
        logger.error(f"文件下载任务执行失败: {str(e)}")
        raise

# 任务函数映射
task_functions = {
    'forecast': forecast_task,
    'rainfall': rainfall_task,
    'warning': warning_task,
    'temperature': temperature_task,
    'download': download_task
}