"""
导出所有A文件数据到数据库的最终脚本
"""

import os
import time
import sys
from export_all import BatchExporter
from database_query import WeatherDataQuery


def main():
    """主函数"""
    print("="*60)
    print("A文件数据库导出工具")
    print("="*60)
    
    # 检查数据目录
    data_dir = "data"
    if not os.path.exists(data_dir):
        print(f"错误: 数据目录不存在 - {data_dir}")
        return
    
    # 统计文件数量
    a_files = [f for f in os.listdir(data_dir) if f.startswith('A') and f.endswith('.TXT')]
    total_files = len(a_files)
    
    print(f"数据目录: {data_dir}")
    print(f"找到A文件: {total_files} 个")
    
    if total_files == 0:
        print("没有找到A文件，退出")
        return
    
    # 创建导出器
    exporter = BatchExporter(
        data_dir=data_dir,
        database_url="sqlite:///weather_data.db",
        max_workers=2
    )
    
    # 获取当前数据库状态
    try:
        query = WeatherDataQuery()
        current_stats = query.get_database_summary()
        print(f"\n当前数据库状态:")
        print(f"  已处理文件: {current_stats.get('files_processed', 0)}")
        print(f"  失败文件: {current_stats.get('files_failed', 0)}")
        print(f"  观测记录: {current_stats.get('observations_count', 0)}")
        print(f"  台站数量: {current_stats.get('stations_count', 0)}")
    except Exception as e:
        print(f"获取数据库状态失败: {e}")
        current_stats = {}
    
    # 询问用户选择
    print(f"\n请选择导出方式:")
    print(f"1. 导出所有文件 ({total_files} 个)")
    print(f"2. 导出样本文件 (前100个)")
    print(f"3. 导出样本文件 (前500个)")
    print(f"4. 导出样本文件 (前1000个)")
    print(f"5. 重新处理失败的文件")
    print(f"6. 查看数据库统计")
    print(f"0. 退出")
    
    while True:
        choice = input(f"\n请输入选择 (0-6): ").strip()
        
        if choice == '0':
            print("退出程序")
            return
        
        elif choice == '1':
            # 导出所有文件
            print(f"\n开始导出所有 {total_files} 个文件...")
            print(f"预计耗时: {total_files/50:.1f} 分钟 (按50文件/分钟估算)")
            
            confirm = input("确认开始导出? (y/N): ").strip().lower()
            if confirm == 'y':
                start_time = time.time()
                exporter.export_all_sequential()
                
                # 显示最终统计
                show_final_statistics(exporter, start_time)
            else:
                print("取消导出")
            break
        
        elif choice == '2':
            print(f"\n开始导出前100个文件...")
            start_time = time.time()
            exporter.export_sample(100)
            show_final_statistics(exporter, start_time)
            break
        
        elif choice == '3':
            print(f"\n开始导出前500个文件...")
            start_time = time.time()
            exporter.export_sample(500)
            show_final_statistics(exporter, start_time)
            break
        
        elif choice == '4':
            print(f"\n开始导出前1000个文件...")
            start_time = time.time()
            exporter.export_sample(1000)
            show_final_statistics(exporter, start_time)
            break
        
        elif choice == '5':
            print(f"\n开始重新处理失败的文件...")
            start_time = time.time()
            exporter.resume_failed()
            show_final_statistics(exporter, start_time)
            break
        
        elif choice == '6':
            show_database_statistics()
            continue
        
        else:
            print("无效选择，请重新输入")


def show_final_statistics(exporter, start_time):
    """显示最终统计信息"""
    try:
        print(f"\n{'='*60}")
        print("最终数据库统计")
        print(f"{'='*60}")
        
        query = WeatherDataQuery()
        
        # 基本统计
        summary = query.get_database_summary()
        print(f"数据库概要:")
        print(f"  台站数量: {summary['stations_count']}")
        print(f"  观测记录: {summary['observations_count']:,}")
        print(f"  气象要素: {summary['elements_count']}")
        print(f"  成功文件: {summary['files_processed']}")
        print(f"  失败文件: {summary['files_failed']}")
        print(f"  时间范围: {summary['date_range']['start']} 到 {summary['date_range']['end']}")
        
        # 要素统计
        elements_stats = query.get_elements_statistics()
        print(f"\n主要气象要素 (前10个):")
        for i, stats in enumerate(elements_stats[:10]):
            print(f"  {i+1:2d}. {stats['element_code']} - {stats['element_name']}: "
                  f"{stats['observations_count']:,}条记录")
        
        # 数据质量
        quality_report = query.get_data_quality_report()
        completeness = quality_report['data_completeness']
        print(f"\n数据质量:")
        print(f"  解析成功率: {completeness['parsing_rate']:.1%}")
        print(f"  数据完整性: {completeness['observations_with_parsed_values']:,}/{completeness['total_observations']:,}")
        
        # 性能统计
        elapsed = time.time() - start_time
        if summary['observations_count'] > 0:
            print(f"\n性能统计:")
            print(f"  总耗时: {elapsed:.1f}秒 ({elapsed/60:.1f}分钟)")
            print(f"  处理速度: {summary['observations_count']/elapsed:.1f}记录/秒")
        
    except Exception as e:
        print(f"获取统计信息失败: {e}")


def show_database_statistics():
    """显示数据库统计信息"""
    try:
        query = WeatherDataQuery()
        
        print(f"\n{'='*50}")
        print("数据库详细统计")
        print(f"{'='*50}")
        
        # 基本概要
        summary = query.get_database_summary()
        print(f"基本信息:")
        for key, value in summary.items():
            print(f"  {key}: {value}")
        
        # 台站信息
        stations = query.get_stations_info()
        print(f"\n台站信息:")
        for station in stations:
            print(f"  台站 {station['station_id']}: "
                  f"({station['latitude']:.4f}, {station['longitude']:.4f}) "
                  f"海拔{station['height']}m "
                  f"{station['observations_count']:,}条记录")
        
        # 月度分布
        monthly_stats = query.get_monthly_statistics()
        if monthly_stats:
            print(f"\n月度数据分布 (最近10个月):")
            for stats in monthly_stats[-10:]:
                print(f"  {stats['year']}-{stats['month']:02d}: "
                      f"{stats['observations_count']:,}条观测")
        
        # 数据质量
        quality_report = query.get_data_quality_report()
        print(f"\n数据质量报告:")
        
        print(f"  文件处理状态:")
        for status, count in quality_report['file_processing'].items():
            print(f"    {status}: {count}个文件")
        
        completeness = quality_report['data_completeness']
        print(f"  数据完整性:")
        print(f"    解析成功率: {completeness['parsing_rate']:.1%}")
        print(f"    已解析记录: {completeness['observations_with_parsed_values']:,}")
        
    except Exception as e:
        print(f"获取统计信息失败: {e}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n程序发生错误: {e}")
        import traceback
        traceback.print_exc()
