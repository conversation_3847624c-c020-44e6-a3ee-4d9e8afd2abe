#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象数据备份和恢复工具
提供数据库备份、恢复、清理等功能
"""

import os
import sys
import shutil
import sqlite3
import argparse
import zipfile
from datetime import datetime
import logging
from database.database import get_db_connection, create_tables

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BackupRestoreManager:
    def __init__(self, db_file='weather_data.db'):
        self.db_file = db_file
        self.backup_dir = 'backups'
        self.ensure_backup_dir()
    
    def ensure_backup_dir(self):
        """确保备份目录存在"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            logger.info(f"创建备份目录: {self.backup_dir}")
    
    def create_backup(self, backup_name=None, compress=True):
        """创建数据库备份"""
        if not os.path.exists(self.db_file):
            logger.error(f"数据库文件不存在: {self.db_file}")
            return None
        
        # 生成备份文件名
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"weather_data_backup_{timestamp}"
        
        try:
            if compress:
                # 创建压缩备份
                backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
                with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    zipf.write(self.db_file, os.path.basename(self.db_file))
                    
                    # 添加日志文件（如果存在）
                    log_files = ['weather_data_processing.log']
                    for log_file in log_files:
                        if os.path.exists(log_file):
                            zipf.write(log_file, log_file)
                    
                    # 添加配置文件
                    config_files = ['config.py']
                    for config_file in config_files:
                        if os.path.exists(config_file):
                            zipf.write(config_file, config_file)
            else:
                # 创建简单备份
                backup_path = os.path.join(self.backup_dir, f"{backup_name}.db")
                shutil.copy2(self.db_file, backup_path)
            
            # 获取备份文件大小
            backup_size = os.path.getsize(backup_path) / 1024 / 1024  # MB
            
            logger.info(f"备份创建成功: {backup_path}")
            logger.info(f"备份文件大小: {backup_size:.2f} MB")
            
            return backup_path
            
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return None
    
    def list_backups(self):
        """列出所有备份文件"""
        if not os.path.exists(self.backup_dir):
            logger.info("备份目录不存在")
            return []
        
        backups = []
        for file in os.listdir(self.backup_dir):
            if file.endswith(('.db', '.zip')):
                file_path = os.path.join(self.backup_dir, file)
                file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                backups.append({
                    'name': file,
                    'path': file_path,
                    'size_mb': file_size,
                    'created_time': file_time
                })
        
        # 按创建时间排序
        backups.sort(key=lambda x: x['created_time'], reverse=True)
        return backups
    
    def restore_backup(self, backup_path, confirm=True):
        """从备份恢复数据库"""
        if not os.path.exists(backup_path):
            logger.error(f"备份文件不存在: {backup_path}")
            return False
        
        if confirm:
            response = input(f"确认要从 {backup_path} 恢复数据库吗？这将覆盖当前数据库 (y/N): ")
            if response.lower() != 'y':
                logger.info("恢复操作已取消")
                return False
        
        try:
            # 备份当前数据库
            if os.path.exists(self.db_file):
                current_backup = f"{self.db_file}.before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(self.db_file, current_backup)
                logger.info(f"当前数据库已备份到: {current_backup}")
            
            # 恢复数据库
            if backup_path.endswith('.zip'):
                # 从压缩文件恢复
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    # 提取数据库文件
                    db_filename = os.path.basename(self.db_file)
                    if db_filename in zipf.namelist():
                        zipf.extract(db_filename, '.')
                        logger.info(f"从压缩备份恢复: {backup_path}")
                    else:
                        logger.error(f"压缩文件中未找到数据库文件: {db_filename}")
                        return False
            else:
                # 从简单备份恢复
                shutil.copy2(backup_path, self.db_file)
                logger.info(f"从备份恢复: {backup_path}")
            
            # 验证恢复的数据库
            if self.verify_database():
                logger.info("数据库恢复成功并通过验证")
                return True
            else:
                logger.error("恢复的数据库验证失败")
                return False
                
        except Exception as e:
            logger.error(f"恢复备份失败: {e}")
            return False
    
    def verify_database(self):
        """验证数据库完整性"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # 检查数据库完整性
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            
            if result[0] != 'ok':
                logger.error(f"数据库完整性检查失败: {result[0]}")
                conn.close()
                return False
            
            # 检查主要表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['hourly_temperature', 'daily_temperature', 'hourly_pressure', 'daily_pressure']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                logger.error(f"缺少必要的数据表: {missing_tables}")
                conn.close()
                return False
            
            conn.close()
            logger.info("数据库验证通过")
            return True
            
        except Exception as e:
            logger.error(f"数据库验证失败: {e}")
            return False
    
    def clean_old_backups(self, keep_count=10):
        """清理旧备份文件"""
        backups = self.list_backups()
        
        if len(backups) <= keep_count:
            logger.info(f"备份文件数量 ({len(backups)}) 未超过保留数量 ({keep_count})")
            return
        
        # 删除多余的备份
        to_delete = backups[keep_count:]
        deleted_count = 0
        
        for backup in to_delete:
            try:
                os.remove(backup['path'])
                logger.info(f"删除旧备份: {backup['name']}")
                deleted_count += 1
            except Exception as e:
                logger.error(f"删除备份失败 {backup['name']}: {e}")
        
        logger.info(f"清理完成，删除了 {deleted_count} 个旧备份文件")
    
    def export_data_sql(self, output_file=None):
        """导出数据为SQL脚本"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"weather_data_export_{timestamp}.sql"
        
        try:
            conn = sqlite3.connect(self.db_file)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                # 写入SQL脚本头部
                f.write("-- 气象数据导出SQL脚本\n")
                f.write(f"-- 导出时间: {datetime.now()}\n")
                f.write("-- 数据库: weather_data.db\n\n")
                
                # 导出数据库结构和数据
                for line in conn.iterdump():
                    f.write(f"{line}\n")
            
            conn.close()
            
            file_size = os.path.getsize(output_file) / 1024 / 1024  # MB
            logger.info(f"SQL导出完成: {output_file}")
            logger.info(f"文件大小: {file_size:.2f} MB")
            
            return output_file
            
        except Exception as e:
            logger.error(f"SQL导出失败: {e}")
            return None
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            stats = {}
            
            # 获取各表记录数
            tables = ['hourly_pressure', 'daily_pressure', 'hourly_temperature', 'daily_temperature',
                     'hourly_humidity', 'daily_humidity', 'hourly_precipitation', 'daily_precipitation']
            
            total_records = 0
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    stats[table] = count
                    total_records += count
                except sqlite3.Error:
                    stats[table] = 0
            
            stats['total_records'] = total_records
            
            # 获取数据时间范围
            try:
                cursor.execute("SELECT MIN(year), MAX(year), MIN(month), MAX(month) FROM hourly_temperature")
                time_range = cursor.fetchone()
                if time_range and time_range[0]:
                    stats['time_range'] = {
                        'start_year': time_range[0],
                        'end_year': time_range[1],
                        'start_month': time_range[2],
                        'end_month': time_range[3]
                    }
            except sqlite3.Error:
                stats['time_range'] = None
            
            # 获取站点数量
            try:
                cursor.execute("SELECT COUNT(DISTINCT station_id) FROM hourly_temperature")
                station_count = cursor.fetchone()[0]
                stats['station_count'] = station_count
            except sqlite3.Error:
                stats['station_count'] = 0
            
            conn.close()
            return stats
            
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='气象数据备份和恢复工具')
    parser.add_argument('action', choices=['backup', 'restore', 'list', 'clean', 'export', 'stats'],
                       help='要执行的操作')
    parser.add_argument('--name', help='备份名称')
    parser.add_argument('--file', help='备份文件路径（用于恢复）')
    parser.add_argument('--compress', action='store_true', help='创建压缩备份')
    parser.add_argument('--keep', type=int, default=10, help='保留的备份数量')
    parser.add_argument('--output', help='输出文件名')
    parser.add_argument('--yes', action='store_true', help='自动确认操作')
    
    args = parser.parse_args()
    
    manager = BackupRestoreManager()
    
    if args.action == 'backup':
        backup_path = manager.create_backup(args.name, args.compress)
        if backup_path:
            print(f"✅ 备份创建成功: {backup_path}")
        else:
            print("❌ 备份创建失败")
            sys.exit(1)
    
    elif args.action == 'restore':
        if not args.file:
            print("❌ 请指定要恢复的备份文件 (--file)")
            sys.exit(1)
        
        success = manager.restore_backup(args.file, not args.yes)
        if success:
            print("✅ 数据库恢复成功")
        else:
            print("❌ 数据库恢复失败")
            sys.exit(1)
    
    elif args.action == 'list':
        backups = manager.list_backups()
        if backups:
            print(f"\n📁 找到 {len(backups)} 个备份文件:")
            print("-" * 80)
            for i, backup in enumerate(backups, 1):
                print(f"{i:2d}. {backup['name']}")
                print(f"     大小: {backup['size_mb']:.2f} MB")
                print(f"     时间: {backup['created_time'].strftime('%Y-%m-%d %H:%M:%S')}")
                print()
        else:
            print("📁 未找到备份文件")
    
    elif args.action == 'clean':
        manager.clean_old_backups(args.keep)
        print(f"✅ 备份清理完成，保留最新 {args.keep} 个备份")
    
    elif args.action == 'export':
        sql_file = manager.export_data_sql(args.output)
        if sql_file:
            print(f"✅ SQL导出成功: {sql_file}")
        else:
            print("❌ SQL导出失败")
            sys.exit(1)
    
    elif args.action == 'stats':
        stats = manager.get_database_stats()
        if stats:
            print("\n📊 数据库统计信息:")
            print("-" * 40)
            print(f"总记录数: {stats['total_records']:,}")
            print(f"站点数量: {stats['station_count']}")
            
            if stats['time_range']:
                tr = stats['time_range']
                print(f"数据时间范围: {tr['start_year']}-{tr['start_month']:02d} 到 {tr['end_year']}-{tr['end_month']:02d}")
            
            print("\n各表记录数:")
            for table, count in stats.items():
                if table.startswith(('hourly_', 'daily_')) and count > 0:
                    print(f"  {table}: {count:,}")
        else:
            print("❌ 无法获取数据库统计信息")

if __name__ == '__main__':
    main()
