#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象数据解析器
整合了降水、气温、风、日照四个气象要素的解析功能
包含数据库导入功能
"""

import re
import calendar
import logging
import os
import sqlite3
import json
from typing import Dict, List, Optional, Union, Tuple, Any
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class WeatherDataParser:
    """统一的气象数据解析器类"""
    
    def __init__(self, file_path: str = None):
        """
        初始化解析器
        
        Args:
            file_path (str): A文件路径
        """
        self.file_path = file_path
        self.lines: List[str] = []
        self.station_info: Optional[Dict[str, Any]] = None
        
    def _load_file(self) -> bool:
        """
        加载文件内容
        
        Returns:
            bool: 加载成功返回True，失败返回False
        """
        try:
            with open(self.file_path, 'r', encoding='gbk') as f:
                self.lines = f.readlines()
            logger.info(f"文件加载成功，共{len(self.lines)}行")
            return True
        except FileNotFoundError:
            logger.error(f"文件未找到: {self.file_path}")
            return False
        except UnicodeDecodeError:
            logger.error("文件编码错误，请确认文件为GBK编码")
            return False
        except Exception as e:
            logger.error(f"加载文件时发生错误: {e}")
            return False
    
    def parse_station_line(self, line: str) -> Optional[Dict[str, Any]]:
        """
        解析台站参数行
        
        Args:
            line (str): 台站参数行
            
        Returns:
            Dict[str, Any]: 台站信息字典，解析失败返回None
        """
        parts = line.split()
        if len(parts) < 12:
            logger.error("错误：台站参数行字段不足。")
            return None
        try:
            station_info = {
                "station_id": parts[0],
                "latitude": parts[1],
                "longitude": parts[2],
                "altitude_observation_field": parts[3],
                "altitude_barometer": parts[4],
                "height_anemometer": parts[5],
                "height_platform": parts[6],
                "observation_method_type": parts[7],
                "observation_items_flags": parts[8],
                "quality_control_indicator": parts[9],
                "year": int(parts[10]),
                "month": int(parts[11])
            }
            return station_info
        except ValueError:
            logger.error(f"错误：台站参数行中的年份或月份不是有效数字: 年份='{parts[10]}', 月份='{parts[11]}'")
            return None

    def process_precip_value(self, val_str: str) -> float:
        """
        处理单个降水数值字符串
        
        Args:
            val_str (str): 降水数值字符串
            
        Returns:
            float: 处理后的数值，trace和微量都转换为0.0
        """
        if not isinstance(val_str, str):
            return 0.0
            
        val_str = val_str.strip()
        
        # 微量降水，转换为0.0
        if val_str in [",,,,", "trace", "Trace", "TRACE"]:
            return 0.0
            
        # 缺测数据
        if val_str in ["////", "----", "A---"]:
            return 0.0
            
        # 处理大降水量（>1000mm）
        if len(val_str) == 4 and val_str.startswith((';', ':')):
            try:
                numeric_part = int(val_str[1:])
                base_value = 0
                if val_str.startswith(';'):
                    base_value = 10000  # ;表示1000+
                elif val_str.startswith(':'):
                    base_value = 20000  # :表示2000+
                return (base_value + numeric_part) / 10.0
            except ValueError:
                return 0.0
                
        # 普通数值
        try:
            return float(val_str) / 10.0  # 单位0.1mm转换为mm
        except ValueError:
            return 0.0

    def process_temperature_value(self, val_str: str) -> Optional[float]:
        """
        处理气温数值字符串
        
        Args:
            val_str (str): 气温数值字符串
            
        Returns:
            Optional[float]: 处理后的气温值（摄氏度），缺测返回None
        """
        if not isinstance(val_str, str):
            return None
            
        val_str = val_str.strip()
        
        # 缺测
        if val_str in ["////", "----", ""]:
            return None
            
        # 湿球结冰情况
        if val_str.startswith(','):
            try:
                return float(val_str[1:]) / 10.0
            except ValueError:
                return None
                
        # 正常温度值
        try:
            # 处理符号位
            if val_str.startswith('-'):
                return -float(val_str[1:]) / 10.0
            elif val_str.startswith('0') or val_str.isdigit():
                return float(val_str) / 10.0
            else:
                return float(val_str) / 10.0
        except ValueError:
            return None

    def find_element_start(self, element_code: str) -> Optional[int]:
        """
        查找指定要素的起始行
        
        Args:
            element_code (str): 要素代码（如'R', 'T', 'F', 'S'）
            
        Returns:
            Optional[int]: 要素起始行索引，未找到返回None
        """
        for i, line in enumerate(self.lines):
            line = line.strip()
            if re.match(f'^{element_code}[0-9A-Z]', line):
                return i
        return None

    def parse_segment_data(self, start_index: int) -> Tuple[List[str], int]:
        """
        解析一个数据段，直到遇到段结束符=
        
        Args:
            start_index (int): 起始行索引
            
        Returns:
            Tuple[List[str], int]: (数据行列表，下一段起始索引)
        """
        data_lines = []
        current_index = start_index
        
        while current_index < len(self.lines):
            line = self.lines[current_index].strip()
            
            # 遇到段结束符
            if line == '=':
                current_index += 1
                break
            elif line.endswith('='):
                # 如果行以=结尾，去掉=后添加数据
                data_content = line[:-1].strip()
                if data_content:
                    data_lines.append(data_content)
                current_index += 1
                break
            elif line.endswith('.'):
                # 日结束符，去掉.后添加数据
                data_content = line[:-1].strip()
                if data_content:
                    data_lines.append(data_content)
                current_index += 1
            else:
                if line:  # 非空行
                    data_lines.append(line)
                current_index += 1
                
        return data_lines, current_index

    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """
        解析单个文件
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        self.file_path = file_path
        return self.parse_all_elements()

    def parse_all_elements(self) -> Dict[str, Any]:
        """
        解析所有气象要素
        
        Returns:
            Dict[str, Any]: 包含所有要素数据的字典
        """
        if not self._load_file():
            return {}
        
        # 解析台站信息
        if self.lines:
            self.station_info = self.parse_station_line(self.lines[0])
            if not self.station_info:
                logger.error("台站信息解析失败")
                return {}
        
        num_days_in_month = calendar.monthrange(
            self.station_info['year'], 
            self.station_info['month']
        )[1]
        
        result = {
            "station_info": self.station_info,
            "precipitation": None,
            "temperature": None,
            "wind": None,
            "sunshine": None
        }
        
        # 解析降水数据
        precip_start = self.find_element_start('R')
        if precip_start is not None:
            way_bit = self.lines[precip_start][1]  # 方式位
            logger.info(f"找到降水数据，方式位: {way_bit}")
            if way_bit == '6':
                precip_data, _ = self.parse_precipitation_data_r6(
                    precip_start + 1, num_days_in_month
                )
                result["precipitation"] = precip_data
        
        # 解析气温数据
        temp_start = self.find_element_start('T')
        if temp_start is not None:
            way_bit = self.lines[temp_start][1]
            logger.info(f"找到气温数据，方式位: {way_bit}")
            temp_data, _ = self.parse_temperature_data(
                temp_start + 1, way_bit, num_days_in_month
            )
            result["temperature"] = temp_data
        
        # 解析风数据
        wind_start = self.find_element_start('F')
        if wind_start is not None:
            way_bit = self.lines[wind_start][1]
            logger.info(f"找到风数据，方式位: {way_bit}")
            wind_data, _ = self.parse_wind_data(
                wind_start + 1, way_bit, num_days_in_month
            )
            result["wind"] = wind_data
        
        # 解析日照数据
        sunshine_start = self.find_element_start('S')
        if sunshine_start is not None:
            way_bit = self.lines[sunshine_start][1]
            logger.info(f"找到日照数据，方式位: {way_bit}")
            sunshine_data, _ = self.parse_sunshine_data(
                sunshine_start + 1, way_bit, num_days_in_month
            )
            result["sunshine"] = sunshine_data
        
        return result

    def parse_precipitation_data_r6(self, start_index: int, num_days: int) -> Tuple[Optional[Dict], int]:
        """
        解析降水数据 (R6格式)
        R6: 定时(3组) + 小时(24组) + 上下月连接值
        """
        # 段1：定时降水
        timed_precip_lines, next_index = self.parse_segment_data(start_index)
        # 段2：小时降水
        hourly_precip_lines, next_index = self.parse_segment_data(next_index)
        # 段3：上下月连接值 (忽略)
        _, next_index = self.parse_segment_data(next_index)

        all_timed_values = " ".join(timed_precip_lines).split()
        all_hourly_values = " ".join(hourly_precip_lines).split()

        timed_data = []
        hourly_data = []
        
        for day in range(1, num_days + 1):
            # 每日3个定时值 (这部分数据与名义日期关联)
            start = (day - 1) * 3
            end = day * 3
            if end <= len(all_timed_values):
                day_vals = [self.process_precip_value(v) for v in all_timed_values[start:end]]
                timed_data.append({"day": day, "values_mm": day_vals})

            # 每日24个小时间隔值 (从前一天21时开始)
            start_h = (day - 1) * 24
            end_h = day * 24
            if end_h <= len(all_hourly_values):
                day_hourly_vals = [self.process_precip_value(v) for v in all_hourly_values[start_h:end_h]]
                
                # 计算正确的起始时间戳
                try:
                    current_day_start_dt = datetime(self.station_info['year'], self.station_info['month'], day, 21, 0) - timedelta(days=1)
                    for hour_index, value in enumerate(day_hourly_vals):
                        timestamp = current_day_start_dt + timedelta(hours=hour_index)
                        hourly_data.append({"timestamp": timestamp, "value": value})
                except ValueError:
                    logger.warning(f"无法为第{day}天创建日期对象，可能月份天数不匹配。")
                    continue

        return {
            "timed_precipitation": timed_data,
            "hourly_precipitation": hourly_data
        }, next_index

    def parse_temperature_data(self, start_index: int, way_bit: str, num_days: int) -> Tuple[Optional[List], int]:
        """
        解析气温数据 (T)
        """
        temp_lines, next_index = self.parse_segment_data(start_index)
        all_values = " ".join(temp_lines).split()
        
        parsed_data = []
        vals_per_day = 0
        if way_bit == '0': vals_per_day = 6   # 4定时 + 日最高/最低
        elif way_bit == '9': vals_per_day = 5   # 3定时 + 日最高/最低
        elif way_bit == 'A': vals_per_day = 26  # 24小时 + 日最高/最低
        elif way_bit == 'B': vals_per_day = 28  # 24小时 + 日最高/最低/时间
        else: return None, next_index

        for day in range(1, num_days + 1):
            start = (day - 1) * vals_per_day
            end = day * vals_per_day
            if end > len(all_values): break

            day_values = all_values[start:end]
            day_data = {"day": day}

            if way_bit in ['A', 'B']:
                hourly_values = [self.process_temperature_value(v) for v in day_values[:24]]
                
                # 计算正确的小时时间戳
                hourly_temps = []
                try:
                    current_day_start_dt = datetime(self.station_info['year'], self.station_info['month'], day, 21, 0) - timedelta(days=1)
                    for hour_index, value in enumerate(hourly_values):
                        timestamp = current_day_start_dt + timedelta(hours=hour_index)
                        hourly_temps.append({"timestamp": timestamp, "value": value})
                    day_data["hourly_temperatures"] = hourly_temps
                except ValueError:
                    logger.warning(f"无法为第{day}天创建日期对象，可能月份天数不匹配。")

                if way_bit == 'A':
                    day_data["daily_max"] = self.process_temperature_value(day_values[24])
                    day_data["daily_min"] = self.process_temperature_value(day_values[25])
                elif way_bit == 'B':
                    day_data["daily_max"] = self.process_temperature_value(day_values[24])
                    day_data["max_time"] = day_values[25]
                    day_data["daily_min"] = self.process_temperature_value(day_values[26])
                    day_data["min_time"] = day_values[27]
            else: # 0, 9
                day_data["timed_temperatures"] = [self.process_temperature_value(v) for v in day_values[:-2]]
                day_data["daily_max"] = self.process_temperature_value(day_values[-2])
                day_data["daily_min"] = self.process_temperature_value(day_values[-1])
            
            parsed_data.append(day_data)
            
        return parsed_data, next_index

    def parse_wind_data(self, start_index: int, way_bit: str, num_days: int) -> Tuple[Optional[List], int]:
        """
        解析风数据 (F)
        """
        # 段1: 2分钟平均风 (定时)
        # Way Bit K/N -> 24次, H -> 3次, E -> 4次
        wind_2min_lines, next_index = self.parse_segment_data(start_index)
        # 段2: 10分钟平均风 (逐小时)
        wind_10min_lines, next_index = self.parse_segment_data(next_index)
        # 段3: 最大/极大风
        wind_max_lines, next_index = self.parse_segment_data(next_index)

        all_2min_values = "".join(wind_2min_lines).split()
        all_10min_values = "".join(wind_10min_lines).split()
        all_max_values = " ".join(wind_max_lines).split()
        
        parsed_data = []
        for day in range(1, num_days + 1):
            day_data = {"day": day}

            # 1. 解析最大/极大风速 (关联名义日期)
            max_start = (day - 1) * 4
            max_end = day * 4
            if max_end <= len(all_max_values):
                day_max_vals = all_max_values[max_start:max_end]
                
                # 最大风
                max_wind_str = day_max_vals[0]
                if len(max_wind_str) == 6 and '/////' not in max_wind_str:
                    day_data["max_wind_speed"] = float(max_wind_str[:3]) / 10.0
                    day_data["max_wind_direction"] = max_wind_str[3:]
                else:
                    day_data["max_wind_speed"] = None
                    day_data["max_wind_direction"] = None
                day_data["max_wind_time"] = day_max_vals[1] if '////' not in day_max_vals[1] else None

                # 极大风
                extreme_wind_str = day_max_vals[2]
                if len(extreme_wind_str) == 6 and '/////' not in extreme_wind_str:
                    day_data["extreme_wind_speed"] = float(extreme_wind_str[:3]) / 10.0
                    day_data["extreme_wind_direction"] = extreme_wind_str[3:]
                else:
                    day_data["extreme_wind_speed"] = None
                    day_data["extreme_wind_direction"] = None
                day_data["extreme_wind_time"] = day_max_vals[3] if '////' not in day_max_vals[3] else None
            
            try:
                current_day_start_dt = datetime(self.station_info['year'], self.station_info['month'], day, 21, 0) - timedelta(days=1)
            except ValueError:
                logger.warning(f"无法为第{day}天创建日期对象，可能月份天数不匹配，跳过该日小时数据。")
                parsed_data.append(day_data)
                continue

            # 2. 解析10分钟平均风 (逐小时)
            hourly_10min_data = []
            h_10min_start = (day - 1) * 24
            h_10min_end = day * 24
            if h_10min_end <= len(all_10min_values):
                day_10min_vals = all_10min_values[h_10min_start:h_10min_end]
                for hour_index, val in enumerate(day_10min_vals):
                    timestamp = current_day_start_dt + timedelta(hours=hour_index)
                    if len(val) == 6:
                        direction = val[:3]
                        speed = float(val[3:]) / 10.0 if val[3:].isdigit() else None
                        hourly_10min_data.append({"timestamp": timestamp, "direction": direction, "speed": speed})
            day_data["hourly_wind_10min"] = hourly_10min_data
            
            # 3. 解析2分钟平均风 (定时，但对于K/N方式位是逐小时)
            hourly_2min_data = []
            if way_bit in ['K', 'N']:
                h_2min_start = (day - 1) * 24
                h_2min_end = day * 24
                if h_2min_end <= len(all_2min_values):
                    day_2min_vals = all_2min_values[h_2min_start:h_2min_end]
                    for hour_index, val in enumerate(day_2min_vals):
                        timestamp = current_day_start_dt + timedelta(hours=hour_index)
                        if len(val) == 6:
                            direction = val[:3]
                            speed = float(val[3:]) / 10.0 if val[3:].isdigit() else None
                            hourly_2min_data.append({"timestamp": timestamp, "direction": direction, "speed": speed})
            day_data["hourly_wind_2min"] = hourly_2min_data
            
            parsed_data.append(day_data)

        return parsed_data, next_index

    def parse_sunshine_data(self, start_index: int, way_bit: str, num_days: int) -> Tuple[Optional[List], int]:
        """
        解析日照数据 (S)
        """
        sunshine_lines, next_index = self.parse_segment_data(start_index)
        all_values = " ".join(sunshine_lines).split()

        parsed_data = []
        vals_per_day = 0
        if way_bit == '0': vals_per_day = 1 # 日总时数
        elif way_bit == 'A': vals_per_day = 27 # 24小时 + 日出日落 + 总时数

        if vals_per_day == 0: return None, next_index

        for day in range(1, num_days + 1):
            start = (day - 1) * vals_per_day
            end = day * vals_per_day
            if end > len(all_values): break
            
            day_values = all_values[start:end]
            day_data = {"day": day}

            if way_bit == 'A':
                day_data["hourly_sunshine"] = [float(v)/10.0 if v.isdigit() else 0.0 for v in day_values[:24]]
                day_data["sunrise_time"] = day_values[24]
                day_data["sunset_time"] = day_values[25]
                day_data["daily_total"] = float(day_values[26])/10.0
            elif way_bit == '0':
                day_data["daily_total"] = float(day_values[0])/10.0
            
            parsed_data.append(day_data)
            
        return parsed_data, next_index


class DatabaseImporter:
    """数据库导入器"""
    
    def __init__(self, db_path: str = "weather_data.db"):
        """
        初始化数据库导入器
        
        Args:
            db_path (str): 数据库文件路径
        """
        self.db_path = db_path
        self.conn = None
        
    def connect(self):
        """连接数据库"""
        self.conn = sqlite3.connect(self.db_path)
        
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            
    def create_tables(self):
        """创建数据表"""
        if not self.conn:
            self.connect()
            
        cursor = self.conn.cursor()
        
        # 台站信息表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS stations (
            station_id TEXT PRIMARY KEY,
            latitude TEXT,
            longitude TEXT,
            altitude_observation_field TEXT,
            altitude_barometer TEXT,
            height_anemometer TEXT,
            height_platform TEXT,
            observation_method_type TEXT,
            observation_items_flags TEXT,
            quality_control_indicator TEXT,
            year INTEGER,
            month INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)

        # 日降水表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS daily_precipitation (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            station_id TEXT NOT NULL,
            date DATE NOT NULL,
            precip_20_08 REAL,  -- 20-08时降水量
            precip_08_20 REAL,  -- 08-20时降水量
            precip_20_20 REAL,  -- 20-20时降水量(日总量)
            year INTEGER,
            month INTEGER,
            day INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(station_id, date)
        )
        """)
        
        # 小时降水表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS hourly_precipitation (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            station_id TEXT NOT NULL,
            date DATE NOT NULL,
            hour INTEGER NOT NULL,
            precipitation REAL,
            year INTEGER,
            month INTEGER,
            day INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(station_id, date, hour)
        )
        """)
        
        # 日气温表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS daily_temperature (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            station_id TEXT NOT NULL,
            date DATE NOT NULL,
            temp_max REAL,
            temp_min REAL,
            temp_02 REAL,
            temp_08 REAL,
            temp_14 REAL,
            temp_20 REAL,
            max_time TEXT,
            min_time TEXT,
            year INTEGER,
            month INTEGER,
            day INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(station_id, date)
        )
        """)
        
        # 小时气温表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS hourly_temperature (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            station_id TEXT NOT NULL,
            date DATE NOT NULL,
            hour INTEGER NOT NULL,
            temperature REAL,
            year INTEGER,
            month INTEGER,
            day INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(station_id, date, hour)
        )
        """)
        
        # 日风表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS daily_wind (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            station_id TEXT NOT NULL,
            date DATE NOT NULL,
            max_wind_speed REAL,
            max_wind_direction TEXT,
            max_wind_time TEXT,
            extreme_wind_speed REAL,
            extreme_wind_direction TEXT,
            extreme_wind_time TEXT,
            year INTEGER,
            month INTEGER,
            day INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(station_id, date)
        )
        """)
        
        # 小时风表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS hourly_wind (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            station_id TEXT NOT NULL,
            date DATE NOT NULL,
            hour INTEGER NOT NULL,
            wind_speed_2min REAL,
            wind_direction_2min TEXT,
            wind_speed_10min REAL,
            wind_direction_10min TEXT,
            year INTEGER,
            month INTEGER,
            day INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(station_id, date, hour)
        )
        """)
        
        # 日日照表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS daily_sunshine (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            station_id TEXT NOT NULL,
            date DATE NOT NULL,
            sunshine_duration REAL,  -- 日照总时数
            sunrise_time TEXT,
            sunset_time TEXT,
            year INTEGER,
            month INTEGER,
            day INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(station_id, date)
        )
        """)
        
        self.conn.commit()
        logger.info("数据表创建完成")
        
    def _import_station_info(self, cursor, station_info: Dict[str, Any]):
        """
        导入或更新台站信息
        """
        cursor.execute("""
        INSERT OR REPLACE INTO stations 
        (station_id, latitude, longitude, altitude_observation_field, altitude_barometer, 
         height_anemometer, height_platform, observation_method_type, observation_items_flags, 
         quality_control_indicator, year, month)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            station_info.get("station_id"), station_info.get("latitude"), station_info.get("longitude"),
            station_info.get("altitude_observation_field"), station_info.get("altitude_barometer"),
            station_info.get("height_anemometer"), station_info.get("height_platform"),
            station_info.get("observation_method_type"), station_info.get("observation_items_flags"),
            station_info.get("quality_control_indicator"), station_info.get("year"), station_info.get("month")
        ))
        logger.info(f"台站信息已导入/更新: {station_info.get('station_id')}")

    def import_data(self, parsed_data: Dict[str, Any]):
        """
        导入解析后的数据
        
        Args:
            parsed_data (Dict[str, Any]): 解析后的数据
        """
        if not self.conn:
            self.connect()
            
        station_info = parsed_data.get("station_info")
        if not station_info or not station_info.get("station_id"):
            logger.warning("数据中无台站信息，跳过导入")
            return

        station_id = station_info["station_id"]
        year = station_info["year"]
        month = station_info["month"]
        
        cursor = self.conn.cursor()
        
        # 导入台站信息
        self._import_station_info(cursor, station_info)

        # 导入降水数据
        if parsed_data.get("precipitation"):
            self._import_precipitation(cursor, parsed_data["precipitation"], station_id, year, month)
            
        # 导入气温数据
        if parsed_data.get("temperature"):
            self._import_temperature(cursor, parsed_data["temperature"], station_id, year, month)
            
        # 导入风数据
        if parsed_data.get("wind"):
            self._import_wind(cursor, parsed_data["wind"], station_id, year, month)
            
        # 导入日照数据
        if parsed_data.get("sunshine"):
            self._import_sunshine(cursor, parsed_data["sunshine"], station_id, year, month)
            
        self.conn.commit()
        logger.info(f"数据导入完成: {station_id} {year}-{month:02d}")
        
    def _import_precipitation(self, cursor, precip_data, station_id, year, month):
        """批量导入降水数据"""
        daily_to_insert = []
        hourly_to_insert = []

        if precip_data.get("timed_precipitation"):
            for day_data in precip_data["timed_precipitation"]:
                day = day_data["day"]
                values = day_data["values_mm"]
                date_str = f"{year}-{month:02d}-{day:02d}"
                daily_to_insert.append((
                    station_id, date_str,
                    values[0] if len(values) > 0 else None,
                    values[1] if len(values) > 1 else None,
                    values[2] if len(values) > 2 else None,
                    year, month, day
                ))
        
        if precip_data.get("hourly_precipitation"):
            for item in precip_data["hourly_precipitation"]:
                ts = item["timestamp"]
                value = item["value"]
                if value is not None:
                    hourly_to_insert.append((
                        station_id, ts.strftime('%Y-%m-%d'), ts.hour, value, ts.year, ts.month, ts.day
                    ))

        if daily_to_insert:
            cursor.executemany("""
            INSERT OR REPLACE INTO daily_precipitation 
            (station_id, date, precip_20_08, precip_08_20, precip_20_20, year, month, day)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, daily_to_insert)
            logger.info(f"批量导入 {len(daily_to_insert)} 条日降水数据")

        if hourly_to_insert:
            cursor.executemany("""
            INSERT OR REPLACE INTO hourly_precipitation 
            (station_id, date, hour, precipitation, year, month, day)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """, hourly_to_insert)
            logger.info(f"批量导入 {len(hourly_to_insert)} 条小时降水数据")

    def _import_temperature(self, cursor, temp_data, station_id, year, month):
        """批量导入气温数据"""
        daily_to_insert = []
        hourly_to_insert = []

        for day_data in temp_data:
            day = day_data["day"]
            date_str = f"{year}-{month:02d}-{day:02d}"
            
            timed_temps = day_data.get("timed_temperatures", [None] * 4)
            daily_to_insert.append((
                station_id, date_str,
                day_data.get("daily_max"), day_data.get("daily_min"),
                timed_temps[0] if len(timed_temps) > 0 else None,
                timed_temps[1] if len(timed_temps) > 1 else None,
                timed_temps[2] if len(timed_temps) > 2 else None,
                timed_temps[3] if len(timed_temps) > 3 else None,
                day_data.get("max_time"), day_data.get("min_time"),
                year, month, day
            ))

            if day_data.get("hourly_temperatures"):
                for item in day_data.get("hourly_temperatures", []):
                    ts = item["timestamp"]
                    value = item["value"]
                    if value is not None:
                        hourly_to_insert.append((
                            station_id, ts.strftime('%Y-%m-%d'), ts.hour, value, ts.year, ts.month, ts.day
                        ))

        if daily_to_insert:
            cursor.executemany("""
            INSERT OR REPLACE INTO daily_temperature 
            (station_id, date, temp_max, temp_min, temp_02, temp_08, temp_14, temp_20, 
             max_time, min_time, year, month, day)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, daily_to_insert)
            logger.info(f"批量导入 {len(daily_to_insert)} 条日气温数据")
        
        if hourly_to_insert:
            cursor.executemany("""
            INSERT OR REPLACE INTO hourly_temperature 
            (station_id, date, hour, temperature, year, month, day)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """, hourly_to_insert)
            logger.info(f"批量导入 {len(hourly_to_insert)} 条小时气温数据")
                        
    def _import_wind(self, cursor, wind_data, station_id, year, month):
        """批量导入风数据"""
        daily_to_insert = []
        hourly_to_insert = []

        for day_data in wind_data:
            day = day_data["day"]
            # 日数据关联名义日期
            date_str = f"{year}-{month:02d}-{day:02d}"
            
            # 每日最大风和极大风
            daily_to_insert.append((
                station_id, date_str,
                day_data.get("max_wind_speed"),
                day_data.get("max_wind_direction"),
                day_data.get("max_wind_time"),
                day_data.get("extreme_wind_speed"),
                day_data.get("extreme_wind_direction"),
                day_data.get("extreme_wind_time"),
                year, month, day
            ))
            
            # 小时数据
            hourly_2min = day_data.get("hourly_wind_2min", [])
            hourly_10min = day_data.get("hourly_wind_10min", [])
            
            # 使用字典按小时组织数据以便合并
            hourly_map = {}
            for item in hourly_2min:
                ts = item["timestamp"]
                if ts not in hourly_map: hourly_map[ts] = {}
                hourly_map[ts]['speed_2min'] = item.get('speed')
                hourly_map[ts]['dir_2min'] = item.get('direction')

            for item in hourly_10min:
                ts = item["timestamp"]
                if ts not in hourly_map: hourly_map[ts] = {}
                hourly_map[ts]['speed_10min'] = item.get('speed')
                hourly_map[ts]['dir_10min'] = item.get('direction')

            for ts, data in hourly_map.items():
                hourly_to_insert.append((
                    station_id, ts.strftime('%Y-%m-%d'), ts.hour,
                    data.get('speed_2min'), data.get('dir_2min'),
                    data.get('speed_10min'), data.get('dir_10min'),
                    ts.year, ts.month, ts.day
                ))
                
        if daily_to_insert:
            cursor.executemany("""
            INSERT OR REPLACE INTO daily_wind 
            (station_id, date, max_wind_speed, max_wind_direction, max_wind_time,
             extreme_wind_speed, extreme_wind_direction, extreme_wind_time, 
             year, month, day)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, daily_to_insert)
            logger.info(f"批量导入 {len(daily_to_insert)} 条日风数据")

        if hourly_to_insert:
            cursor.executemany("""
            INSERT OR REPLACE INTO hourly_wind 
            (station_id, date, hour, wind_speed_2min, wind_direction_2min, wind_speed_10min, wind_direction_10min, year, month, day)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, hourly_to_insert)
            logger.info(f"批量导入 {len(hourly_to_insert)} 条小时风数据")
                 
    def _import_sunshine(self, cursor, sunshine_data, station_id, year, month):
        """批量导入日照数据"""
        daily_to_insert = []

        for day_data in sunshine_data:
            day = day_data["day"]
            date_str = f"{year}-{month:02d}-{day:02d}"
            
            daily_to_insert.append((
                station_id, date_str,
                day_data.get("daily_total"),
                day_data.get("sunrise_time"),
                day_data.get("sunset_time"),
                year, month, day
            ))
            
        if daily_to_insert:
            cursor.executemany("""
            INSERT OR REPLACE INTO daily_sunshine 
            (station_id, date, sunshine_duration, sunrise_time, sunset_time, year, month, day)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, daily_to_insert)
            logger.info(f"批量导入 {len(daily_to_insert)} 条日日照数据")


def process_data_folder(data_folder: str, db_path: str = "weather_data.db"):
    """
    处理data文件夹中的所有文件并导入数据库
    
    Args:
        data_folder (str): data文件夹路径
        db_path (str): 数据库文件路径
    """
    parser = WeatherDataParser()
    importer = DatabaseImporter(db_path)
    
    # 创建数据表
    importer.create_tables()
    
    # 获取所有.TXT文件
    txt_files = []
    for root, dirs, files in os.walk(data_folder):
        for file in files:
            if file.upper().endswith('.TXT'):
                txt_files.append(os.path.join(root, file))
    
    logger.info(f"找到 {len(txt_files)} 个TXT文件")
    
    # 处理每个文件
    success_count = 0
    error_count = 0
    
    for file_path in txt_files:
        try:
            logger.info(f"正在处理文件: {file_path}")
            parsed_data = parser.parse_file(file_path)
            
            if parsed_data and parsed_data.get("station_info"):
                importer.import_data(parsed_data)
                success_count += 1
                logger.info(f"成功处理: {file_path}")
            else:
                logger.warning(f"文件解析失败: {file_path}")
                error_count += 1
                
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时发生错误: {e}")
            error_count += 1
    
    importer.close()
    logger.info(f"处理完成！成功: {success_count}, 失败: {error_count}")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  处理单个文件: python 气象数据解析器.py <A文件路径> [数据库路径]")
        print("  处理data文件夹: python 气象数据解析器.py --import-folder <data文件夹路径> [数据库路径]")
        sys.exit(1)
    
    if sys.argv[1] == "--import-folder":
        if len(sys.argv) < 3:
            print("请指定data文件夹路径")
            sys.exit(1)
        data_folder = sys.argv[2]
        db_path = sys.argv[3] if len(sys.argv) > 3 else "weather_data.db"
        process_data_folder(data_folder, db_path)
    else:
        # 处理单个文件
        file_path = sys.argv[1]
        db_path = sys.argv[2] if len(sys.argv) > 2 else "weather_data.db"

        parser = WeatherDataParser()
        parsed_data = parser.parse_file(file_path)
        
        if parsed_data and parsed_data.get("station_info"):
            print("解析成功！")
            
            # 导入数据库
            importer = DatabaseImporter(db_path)
            importer.create_tables()
            importer.import_data(parsed_data)
            importer.close()
            print(f"数据已导入到: {db_path}")

            # 保存到JSON文件
            output_file = os.path.splitext(file_path)[0] + "_parsed.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(parsed_data, f, ensure_ascii=False, indent=2, default=str)
            print(f"解析详情已保存到: {output_file}")
        else:
            print("数据解析失败！")


if __name__ == "__main__":
    main() 