#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象数据处理系统启动脚本
提供简化的命令行界面来使用系统各个功能
"""

import os
import sys
import argparse
import subprocess
from datetime import datetime
import config

def print_banner():
    """打印系统横幅"""
    print("=" * 70)
    print("           气象数据处理系统 (Weather Data Processing System)")
    print("                     基于中国气象局A文件格式")
    print("=" * 70)

def print_menu():
    """打印主菜单"""
    print("\n可用功能:")
    print("1. 处理A文件 (process)")
    print("2. 数据验证 (validate)")
    print("3. 数据导出 (export)")
    print("4. 系统测试 (test)")
    print("5. 查看帮助 (help)")
    print("6. 退出 (exit)")

def process_files(args=None):
    """处理A文件"""
    print("\n📁 A文件处理")
    print("-" * 30)
    
    if args and args.file:
        # 处理指定文件
        file_path = args.file
        if os.path.exists(file_path):
            print(f"处理文件: {file_path}")
            result = subprocess.run([sys.executable, 'main.py', file_path], 
                                  capture_output=True, text=True, encoding='utf-8')
            print(result.stdout)
            if result.stderr:
                print("错误:", result.stderr)
        else:
            print(f"❌ 文件不存在: {file_path}")
    else:
        # 批量处理
        a_files = [f for f in os.listdir('.') if f.startswith('A') and f.endswith('.TXT')]
        
        if not a_files:
            print("❌ 当前目录下未找到A文件 (A*.TXT)")
            return
        
        print(f"找到 {len(a_files)} 个A文件:")
        for i, file in enumerate(a_files, 1):
            print(f"  {i}. {file}")
        
        choice = input("\n选择处理方式:\n1. 处理所有文件\n2. 选择特定文件\n请输入选择 (1-2): ")
        
        if choice == '1':
            print("\n开始批量处理...")
            result = subprocess.run([sys.executable, 'main.py'], 
                                  capture_output=True, text=True, encoding='utf-8')
            print(result.stdout)
            if result.stderr:
                print("错误:", result.stderr)
        elif choice == '2':
            try:
                file_num = int(input("请输入文件编号: ")) - 1
                if 0 <= file_num < len(a_files):
                    selected_file = a_files[file_num]
                    print(f"\n处理文件: {selected_file}")
                    result = subprocess.run([sys.executable, 'main.py', selected_file], 
                                          capture_output=True, text=True, encoding='utf-8')
                    print(result.stdout)
                    if result.stderr:
                        print("错误:", result.stderr)
                else:
                    print("❌ 无效的文件编号")
            except ValueError:
                print("❌ 请输入有效的数字")

def validate_data(args=None):
    """数据验证"""
    print("\n🔍 数据验证")
    print("-" * 30)
    
    result = subprocess.run([sys.executable, 'data_validator.py'], 
                          capture_output=True, text=True, encoding='utf-8')
    print(result.stdout)
    if result.stderr:
        print("错误:", result.stderr)

def export_data(args=None):
    """数据导出"""
    print("\n📤 数据导出")
    print("-" * 30)
    
    if args and args.station:
        # 命令行参数导出
        cmd = [sys.executable, 'data_exporter.py', '--station', args.station]
        
        if args.start_date:
            cmd.extend(['--start-date', args.start_date])
        if args.end_date:
            cmd.extend(['--end-date', args.end_date])
        if args.elements:
            cmd.extend(['--elements'] + args.elements)
        if args.format:
            cmd.extend(['--format', args.format])
        if args.output:
            cmd.extend(['--output', args.output])
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        print(result.stdout)
        if result.stderr:
            print("错误:", result.stderr)
    else:
        # 交互式导出
        # 首先获取可用站点
        result = subprocess.run([sys.executable, 'data_exporter.py', '--list-stations'], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if "可用站点" in result.stdout:
            print(result.stdout)
            
            station = input("\n请输入站点ID: ").strip()
            if not station:
                print("❌ 站点ID不能为空")
                return
            
            print("\n可选气象要素:")
            elements = ['temperature', 'pressure', 'humidity', 'precipitation', 'wind']
            for i, elem in enumerate(elements, 1):
                print(f"  {i}. {elem}")
            
            elem_choice = input("请选择要素 (用空格分隔多个选择，如: 1 2 3): ").strip()
            selected_elements = []
            
            if elem_choice:
                try:
                    indices = [int(x) - 1 for x in elem_choice.split()]
                    selected_elements = [elements[i] for i in indices if 0 <= i < len(elements)]
                except ValueError:
                    print("使用默认要素: temperature, pressure, humidity")
                    selected_elements = ['temperature', 'pressure', 'humidity']
            else:
                selected_elements = ['temperature', 'pressure', 'humidity']
            
            format_choice = input("选择导出格式 (csv/excel) [csv]: ").strip() or 'csv'
            
            # 构建导出命令
            cmd = [sys.executable, 'data_exporter.py', 
                   '--station', station,
                   '--elements'] + selected_elements + [
                   '--format', format_choice]
            
            print(f"\n导出站点 {station} 的数据...")
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            print(result.stdout)
            if result.stderr:
                print("错误:", result.stderr)
        else:
            print("❌ 无法获取站点列表，请确保数据库中有数据")

def run_tests():
    """运行系统测试"""
    print("\n🧪 系统测试")
    print("-" * 30)
    
    result = subprocess.run([sys.executable, 'test_system.py'], 
                          capture_output=True, text=True, encoding='utf-8')
    print(result.stdout)
    if result.stderr:
        print("错误:", result.stderr)

def show_help():
    """显示帮助信息"""
    print("\n📖 帮助信息")
    print("-" * 30)
    print("""
气象数据处理系统使用说明:

1. A文件处理:
   - 支持单文件和批量处理
   - 自动解析气象要素数据
   - 存储到SQLite数据库

2. 数据验证:
   - 检查数据完整性
   - 验证数据质量
   - 生成统计报告

3. 数据导出:
   - 支持CSV和Excel格式
   - 可按站点、时间、要素筛选
   - 支持批量导出

4. 系统测试:
   - 验证各模块功能
   - 检查系统集成
   - 诊断问题

命令行使用示例:
  python run_weather_system.py process --file A54424-202207.TXT
  python run_weather_system.py export --station 54424 --format csv
  python run_weather_system.py validate
  python run_weather_system.py test

支持的气象要素:
""")
    
    for code, info in config.WEATHER_ELEMENTS.items():
        print(f"  {code}: {info['name']} ({info['unit']}) - {info['description']}")

def interactive_mode():
    """交互模式"""
    print_banner()
    
    while True:
        print_menu()
        choice = input("\n请选择功能 (1-6): ").strip()
        
        if choice == '1' or choice.lower() == 'process':
            process_files()
        elif choice == '2' or choice.lower() == 'validate':
            validate_data()
        elif choice == '3' or choice.lower() == 'export':
            export_data()
        elif choice == '4' or choice.lower() == 'test':
            run_tests()
        elif choice == '5' or choice.lower() == 'help':
            show_help()
        elif choice == '6' or choice.lower() == 'exit':
            print("\n👋 感谢使用气象数据处理系统！")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='气象数据处理系统')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 处理文件命令
    process_parser = subparsers.add_parser('process', help='处理A文件')
    process_parser.add_argument('--file', help='指定要处理的文件')
    
    # 数据验证命令
    validate_parser = subparsers.add_parser('validate', help='数据验证')
    
    # 数据导出命令
    export_parser = subparsers.add_parser('export', help='数据导出')
    export_parser.add_argument('--station', required=True, help='站点ID')
    export_parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    export_parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    export_parser.add_argument('--elements', nargs='+', 
                              choices=['temperature', 'pressure', 'humidity', 'precipitation', 'wind'],
                              default=['temperature', 'pressure', 'humidity'],
                              help='要导出的气象要素')
    export_parser.add_argument('--format', choices=['csv', 'excel'], default='csv', help='导出格式')
    export_parser.add_argument('--output', help='输出文件名')
    
    # 系统测试命令
    test_parser = subparsers.add_parser('test', help='运行系统测试')
    
    # 帮助命令
    help_parser = subparsers.add_parser('help', help='显示帮助信息')
    
    args = parser.parse_args()
    
    # 初始化配置
    config.init_config()
    
    if args.command == 'process':
        process_files(args)
    elif args.command == 'validate':
        validate_data(args)
    elif args.command == 'export':
        export_data(args)
    elif args.command == 'test':
        run_tests()
    elif args.command == 'help':
        show_help()
    else:
        # 没有命令参数时进入交互模式
        interactive_mode()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，再见！")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        sys.exit(1)
