#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
气象数据处理系统配置文件
"""

import os

# 数据库配置
DATABASE_CONFIG = {
    'database_file': 'weather_data.db',
    'timeout': 30,  # 数据库连接超时时间（秒）
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'weather_data_processing.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# A文件处理配置
A_FILE_CONFIG = {
    'encoding': 'gbk',  # A文件编码
    'file_pattern': 'A*.TXT',  # 默认文件匹配模式
    'batch_size': 1000,  # 批量插入数据库的记录数
}

# 数据质量控制配置
QUALITY_CONTROL = {
    'temperature': {
        'min_value': -60.0,  # 最低温度（摄氏度）
        'max_value': 60.0,   # 最高温度（摄氏度）
    },
    'pressure': {
        'min_value': 800.0,  # 最低气压（hPa）
        'max_value': 1100.0, # 最高气压（hPa）
    },
    'humidity': {
        'min_value': 0,      # 最低湿度（%）
        'max_value': 100,    # 最高湿度（%）
    },
    'precipitation': {
        'min_value': 0.0,    # 最低降水量（mm）
        'max_value': 1000.0, # 最高日降水量（mm）
    },
    'wind_speed': {
        'min_value': 0.0,    # 最低风速（m/s）
        'max_value': 100.0,  # 最高风速（m/s）
    },
    'visibility': {
        'min_value': 0.0,    # 最低能见度（km）
        'max_value': 100.0,  # 最高能见度（km）
    }
}

# 导出配置
EXPORT_CONFIG = {
    'default_format': 'csv',
    'csv_encoding': 'utf-8-sig',  # 支持中文的CSV编码
    'excel_engine': 'openpyxl',
    'max_records_per_export': 100000,  # 单次导出最大记录数
}

# 气象要素配置
WEATHER_ELEMENTS = {
    'P': {
        'name': '气压',
        'english_name': 'pressure',
        'unit': 'hPa',
        'description': '本站气压和海平面气压'
    },
    'T': {
        'name': '气温',
        'english_name': 'temperature',
        'unit': '°C',
        'description': '干球温度'
    },
    'I': {
        'name': '湿球温度',
        'english_name': 'wet_bulb_temperature',
        'unit': '°C',
        'description': '湿球温度和露点温度'
    },
    'E': {
        'name': '水汽压',
        'english_name': 'vapor_pressure',
        'unit': 'hPa',
        'description': '水汽压'
    },
    'U': {
        'name': '相对湿度',
        'english_name': 'humidity',
        'unit': '%',
        'description': '相对湿度'
    },
    'N': {
        'name': '云量',
        'english_name': 'cloud_amount',
        'unit': '成',
        'description': '总云量和低云量'
    },
    'H': {
        'name': '云高',
        'english_name': 'cloud_height',
        'unit': 'm',
        'description': '云底高度'
    },
    'C': {
        'name': '云状',
        'english_name': 'cloud_type',
        'unit': '',
        'description': '云状'
    },
    'V': {
        'name': '能见度',
        'english_name': 'visibility',
        'unit': 'km',
        'description': '水平能见度'
    },
    'R': {
        'name': '降水量',
        'english_name': 'precipitation',
        'unit': 'mm',
        'description': '降水量'
    },
    'W': {
        'name': '天气现象',
        'english_name': 'weather_phenomenon',
        'unit': '',
        'description': '天气现象编码'
    },
    'F': {
        'name': '风',
        'english_name': 'wind',
        'unit': 'm/s',
        'description': '风向风速'
    },
    'L': {
        'name': '蒸发量',
        'english_name': 'evaporation',
        'unit': 'mm',
        'description': '蒸发量'
    },
    'S': {
        'name': '日照时数',
        'english_name': 'sunshine_hours',
        'unit': 'h',
        'description': '日照时数'
    },
    'D': {
        'name': '浅层地温',
        'english_name': 'shallow_ground_temperature',
        'unit': '°C',
        'description': '浅层地温'
    },
    'K': {
        'name': '深层地温',
        'english_name': 'deep_ground_temperature',
        'unit': '°C',
        'description': '深层地温'
    }
}

# 系统路径配置
PATHS = {
    'data_dir': 'data',
    'log_dir': 'logs',
    'export_dir': 'exports',
    'backup_dir': 'backups',
}

# 创建必要的目录
def create_directories():
    """创建系统所需的目录"""
    for path in PATHS.values():
        if not os.path.exists(path):
            os.makedirs(path)

# 获取配置值的辅助函数
def get_config(section, key, default=None):
    """获取配置值"""
    config_dict = globals().get(section.upper() + '_CONFIG', {})
    return config_dict.get(key, default)

def get_quality_control_limits(element):
    """获取指定要素的质量控制限值"""
    return QUALITY_CONTROL.get(element, {})

def get_element_info(element_code):
    """获取气象要素信息"""
    return WEATHER_ELEMENTS.get(element_code, {})

# 环境变量覆盖配置
def load_env_config():
    """从环境变量加载配置"""
    # 数据库配置
    if os.getenv('WEATHER_DB_FILE'):
        DATABASE_CONFIG['database_file'] = os.getenv('WEATHER_DB_FILE')
    
    # 日志级别
    if os.getenv('WEATHER_LOG_LEVEL'):
        LOGGING_CONFIG['level'] = os.getenv('WEATHER_LOG_LEVEL')
    
    # A文件编码
    if os.getenv('WEATHER_FILE_ENCODING'):
        A_FILE_CONFIG['encoding'] = os.getenv('WEATHER_FILE_ENCODING')

# 初始化配置
def init_config():
    """初始化配置"""
    create_directories()
    load_env_config()

if __name__ == '__main__':
    # 测试配置
    init_config()
    print("配置初始化完成")
    print(f"数据库文件: {DATABASE_CONFIG['database_file']}")
    print(f"日志级别: {LOGGING_CONFIG['level']}")
    print(f"支持的气象要素: {len(WEATHER_ELEMENTS)} 个")
    
    # 显示所有支持的气象要素
    print("\n支持的气象要素:")
    for code, info in WEATHER_ELEMENTS.items():
        print(f"  {code}: {info['name']} ({info['unit']}) - {info['description']}")
