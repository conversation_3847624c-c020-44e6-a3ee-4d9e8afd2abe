
import re
import calendar
import logging

logger = logging.getLogger(__name__)

def parse_pressure(context):
    """
    解析气压数据块
    根据A文件格式规范解析气压数据
    """
    station_id = context['station_id']
    year = context['year']
    month = context['month']
    mode = context['mode']
    data = context['data']

    hourly_data = []
    daily_data = []

    # 根据方式位确定数据格式
    # C: 本站气压每天24次定时值和日最高、最低值及出现时间共28组
    # D: 本站气压每天24次定时值和日最高、最低值及出现时间共28组
    # B: 本站气压每天24次定时及自记日最高、最低值共26组

    logger.debug(f"解析气压数据，方式位: {mode}")

    # 按行分割数据，每行代表一天
    lines = [line.strip() for line in data.split('.') if line.strip()]

    # 获取该月的天数
    days_in_month = calendar.monthrange(year, month)[1]

    for day_index, line in enumerate(lines):
        if day_index >= days_in_month:
            break

        day = day_index + 1
        values = line.split()

        if not values:
            continue

        try:
            # 解析24小时数据 (从21时开始到20时)
            hour_values = []
            for i in range(min(24, len(values))):
                val_str = values[i]
                if val_str != '////':
                    try:
                        pressure = int(val_str)
                        # 根据文档，小于1000.0hPa时千位不录入
                        if pressure < 5000:
                            pressure += 10000
                        hour_values.append(pressure / 10.0)
                    except ValueError:
                        hour_values.append(None)
                else:
                    hour_values.append(None)

            # 添加小时数据
            for i, pressure_val in enumerate(hour_values):
                if pressure_val is not None:
                    # 小时从21开始，到20结束
                    hour = (21 + i) % 24
                    hourly_data.append({
                        'station_id': station_id,
                        'year': year,
                        'month': month,
                        'day': day,
                        'hour': hour,
                        'pressure': pressure_val
                    })

            # 解析日最高最低值
            if mode in ['C', 'D'] and len(values) >= 28:
                # 方式位C和D: 28组数据，包含最高最低值及出现时间
                try:
                    max_p_str = values[24]  # 最高值
                    min_p_str = values[26]  # 最低值

                    max_pressure = None
                    min_pressure = None

                    if max_p_str != '////':
                        max_p = int(max_p_str)
                        if max_p < 5000:
                            max_p += 10000
                        max_pressure = max_p / 10.0

                    if min_p_str != '////':
                        min_p = int(min_p_str)
                        if min_p < 5000:
                            min_p += 10000
                        min_pressure = min_p / 10.0

                    if max_pressure is not None or min_pressure is not None:
                        daily_data.append({
                            'station_id': station_id,
                            'year': year,
                            'month': month,
                            'day': day,
                            'max_pressure': max_pressure,
                            'min_pressure': min_pressure
                        })

                except (ValueError, IndexError) as e:
                    logger.warning(f"解析日极值时出错 {station_id} {year}-{month}-{day}: {e}")

            elif mode == 'B' and len(values) >= 26:
                # 方式位B: 26组数据，包含最高最低值
                try:
                    max_p_str = values[24]  # 最高值
                    min_p_str = values[25]  # 最低值

                    max_pressure = None
                    min_pressure = None

                    if max_p_str != '////':
                        max_p = int(max_p_str)
                        if max_p < 5000:
                            max_p += 10000
                        max_pressure = max_p / 10.0

                    if min_p_str != '////':
                        min_p = int(min_p_str)
                        if min_p < 5000:
                            min_p += 10000
                        min_pressure = min_p / 10.0

                    if max_pressure is not None or min_pressure is not None:
                        daily_data.append({
                            'station_id': station_id,
                            'year': year,
                            'month': month,
                            'day': day,
                            'max_pressure': max_pressure,
                            'min_pressure': min_pressure
                        })

                except (ValueError, IndexError) as e:
                    logger.warning(f"解析日极值时出错 {station_id} {year}-{month}-{day}: {e}")

        except Exception as e:
            logger.error(f"解析第{day}天气压数据时出错: {e}")
            continue

    logger.debug(f"气压解析完成: {len(hourly_data)} 小时记录, {len(daily_data)} 日记录")
    return hourly_data, daily_data
