# -*- coding: utf-8 -*-
"""
数据模型定义
定义数据库表结构
"""

from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime

Base = declarative_base()


class Station(Base):
    """台站信息表"""
    __tablename__ = 'stations'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    station_id = Column(String(10), unique=True, nullable=False, comment='区站号')
    station_name = Column(String(100), comment='台站名称')
    latitude = Column(Float, comment='纬度')
    longitude = Column(Float, comment='经度')
    altitude = Column(Float, comment='观测场拔海高度(0.1m)')
    pressure_altitude = Column(Float, comment='气压感应器拔海高度(0.1m)')
    wind_height = Column(Float, comment='风速感应器距地高度(0.1m)')
    platform_height = Column(Float, comment='观测平台距地高度(0.1m)')
    observation_mode = Column(String(10), comment='观测方式和测站类别')
    observation_items = Column(String(50), comment='观测项目标识')
    quality_control = Column(String(5), comment='质量控制指示码')
    province = Column(String(50), comment='省份')
    city = Column(String(50), comment='城市')
    address = Column(String(200), comment='详细地址')
    environment = Column(String(100), comment='地理环境')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关联关系
    hourly_data = relationship("HourlyData", back_populates="station")
    daily_data = relationship("DailyData", back_populates="station")


class HourlyData(Base):
    """小时数据表"""
    __tablename__ = 'hourly_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    station_id = Column(String(10), ForeignKey('stations.station_id'), nullable=False, comment='区站号')
    observation_time = Column(DateTime, nullable=False, comment='观测时间')
    year = Column(Integer, nullable=False, comment='年份')
    month = Column(Integer, nullable=False, comment='月份')
    day = Column(Integer, nullable=False, comment='日期')
    hour = Column(Integer, nullable=False, comment='小时')
    
    # 气压相关
    pressure = Column(Float, comment='本站气压(0.1hPa)')
    sea_level_pressure = Column(Float, comment='海平面气压(0.1hPa)')
    
    # 温度相关
    temperature = Column(Float, comment='气温(0.1℃)')
    wet_bulb_temp = Column(Float, comment='湿球温度(0.1℃)')
    dew_point_temp = Column(Float, comment='露点温度(0.1℃)')
    ground_temp = Column(Float, comment='草面温度(0.1℃)')
    
    # 湿度相关
    water_vapor_pressure = Column(Float, comment='水汽压(0.1hPa)')
    relative_humidity = Column(Float, comment='相对湿度(%)')
    
    # 云相关
    total_cloud = Column(Float, comment='总云量(成)')
    low_cloud = Column(Float, comment='低云量(成)')
    cloud_height = Column(Float, comment='云高(m)')
    cloud_type = Column(String(20), comment='云状')
    
    # 能见度
    visibility = Column(Float, comment='能见度(0.1km)')
    
    # 降水
    precipitation = Column(Float, comment='降水量(0.1mm)')
    
    # 风
    wind_direction = Column(String(10), comment='风向')
    wind_speed = Column(Float, comment='风速(0.1m/s)')
    wind_direction_2min = Column(String(10), comment='2分钟平均风向')
    wind_speed_2min = Column(Float, comment='2分钟平均风速(0.1m/s)')
    wind_direction_10min = Column(String(10), comment='10分钟平均风向')
    wind_speed_10min = Column(Float, comment='10分钟平均风速(0.1m/s)')
    
    # 地温
    ground_temp_0cm = Column(Float, comment='0cm地温(0.1℃)')
    ground_temp_5cm = Column(Float, comment='5cm地温(0.1℃)')
    ground_temp_10cm = Column(Float, comment='10cm地温(0.1℃)')
    ground_temp_15cm = Column(Float, comment='15cm地温(0.1℃)')
    ground_temp_20cm = Column(Float, comment='20cm地温(0.1℃)')
    ground_temp_40cm = Column(Float, comment='40cm地温(0.1℃)')
    
    # 深层地温
    deep_temp_80cm = Column(Float, comment='80cm地温(0.1℃)')
    deep_temp_160cm = Column(Float, comment='160cm地温(0.1℃)')
    deep_temp_320cm = Column(Float, comment='320cm地温(0.1℃)')
    
    # 其他
    evaporation = Column(Float, comment='蒸发量(0.1mm)')
    sunshine_duration = Column(Float, comment='日照时数(0.1h)')
    snow_depth = Column(Float, comment='雪深(cm)')
    snow_pressure = Column(Float, comment='雪压(0.1g/cm2)')
    frozen_soil_depth = Column(Float, comment='冻土深度(cm)')
    
    # 天气现象
    weather_phenomenon = Column(String(100), comment='天气现象')
    
    # 质量控制
    data_quality = Column(String(10), comment='数据质量标识')
    is_valid = Column(Boolean, default=True, comment='数据是否有效')
    
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    
    # 关联关系
    station = relationship("Station", back_populates="hourly_data")


class DailyData(Base):
    """日数据表"""
    __tablename__ = 'daily_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    station_id = Column(String(10), ForeignKey('stations.station_id'), nullable=False, comment='区站号')
    observation_date = Column(DateTime, nullable=False, comment='观测日期')
    year = Column(Integer, nullable=False, comment='年份')
    month = Column(Integer, nullable=False, comment='月份')
    day = Column(Integer, nullable=False, comment='日期')
    
    # 气压统计
    pressure_avg = Column(Float, comment='平均气压(0.1hPa)')
    pressure_max = Column(Float, comment='最高气压(0.1hPa)')
    pressure_min = Column(Float, comment='最低气压(0.1hPa)')
    pressure_max_time = Column(String(10), comment='最高气压出现时间')
    pressure_min_time = Column(String(10), comment='最低气压出现时间')
    
    # 温度统计
    temperature_avg = Column(Float, comment='平均气温(0.1℃)')
    temperature_max = Column(Float, comment='最高气温(0.1℃)')
    temperature_min = Column(Float, comment='最低气温(0.1℃)')
    temperature_max_time = Column(String(10), comment='最高气温出现时间')
    temperature_min_time = Column(String(10), comment='最低气温出现时间')
    
    # 湿度统计
    humidity_avg = Column(Float, comment='平均相对湿度(%)')
    humidity_min = Column(Float, comment='最小相对湿度(%)')
    humidity_min_time = Column(String(10), comment='最小湿度出现时间')
    
    # 风统计
    wind_speed_avg = Column(Float, comment='平均风速(0.1m/s)')
    wind_speed_max = Column(Float, comment='最大风速(0.1m/s)')
    wind_speed_max_time = Column(String(10), comment='最大风速出现时间')
    wind_direction_max = Column(String(10), comment='最大风速时风向')
    wind_speed_extreme = Column(Float, comment='极大风速(0.1m/s)')
    wind_speed_extreme_time = Column(String(10), comment='极大风速出现时间')
    wind_direction_extreme = Column(String(10), comment='极大风速时风向')
    
    # 降水统计
    precipitation_total = Column(Float, comment='日降水量(0.1mm)')
    precipitation_20_08 = Column(Float, comment='20-08时降水量(0.1mm)')
    precipitation_08_20 = Column(Float, comment='08-20时降水量(0.1mm)')
    precipitation_20_20 = Column(Float, comment='20-20时降水量(0.1mm)')
    precipitation_max_1h = Column(Float, comment='1小时最大降水量(0.1mm)')
    precipitation_max_10min = Column(Float, comment='10分钟最大降水量(0.1mm)')
    
    # 蒸发量
    evaporation_total = Column(Float, comment='日蒸发量(0.1mm)')
    evaporation_small = Column(Float, comment='小型蒸发量(0.1mm)')
    evaporation_large = Column(Float, comment='大型蒸发量(0.1mm)')
    
    # 日照
    sunshine_total = Column(Float, comment='日照总时数(0.1h)')
    sunshine_percentage = Column(Float, comment='日照百分率(%)')
    
    # 地温统计
    ground_temp_0cm_avg = Column(Float, comment='0cm平均地温(0.1℃)')
    ground_temp_0cm_max = Column(Float, comment='0cm最高地温(0.1℃)')
    ground_temp_0cm_min = Column(Float, comment='0cm最低地温(0.1℃)')
    
    # 积雪
    snow_depth_avg = Column(Float, comment='平均雪深(cm)')
    snow_depth_max = Column(Float, comment='最大雪深(cm)')
    
    # 天气现象统计
    weather_phenomena = Column(Text, comment='天气现象记录')
    
    # 质量控制
    data_quality = Column(String(10), comment='数据质量标识')
    is_valid = Column(Boolean, default=True, comment='数据是否有效')
    
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    
    # 关联关系
    station = relationship("Station", back_populates="daily_data")


class TemperatureData(Base):
    """温度数据表"""
    __tablename__ = 'temperature_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    station_id = Column(String(10), nullable=False, comment='区站号')
    observation_time = Column(DateTime, nullable=False, comment='观测时间')
    data_type = Column(String(20), nullable=False, comment='数据类型(hourly/daily)')
    
    # 气温
    air_temperature = Column(Float, comment='气温(0.1℃)')
    air_temp_max = Column(Float, comment='最高气温(0.1℃)')
    air_temp_min = Column(Float, comment='最低气温(0.1℃)')
    air_temp_max_time = Column(String(10), comment='最高气温时间')
    air_temp_min_time = Column(String(10), comment='最低气温时间')
    
    # 地面温度
    ground_temperature = Column(Float, comment='地面温度(0.1℃)')
    ground_temp_max = Column(Float, comment='最高地面温度(0.1℃)')
    ground_temp_min = Column(Float, comment='最低地面温度(0.1℃)')
    
    # 草面温度
    grass_temperature = Column(Float, comment='草面温度(0.1℃)')
    grass_temp_max = Column(Float, comment='最高草面温度(0.1℃)')
    grass_temp_min = Column(Float, comment='最低草面温度(0.1℃)')
    
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')


class PrecipitationData(Base):
    """降水数据表"""
    __tablename__ = 'precipitation_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    station_id = Column(String(10), nullable=False, comment='区站号')
    observation_time = Column(DateTime, nullable=False, comment='观测时间')
    data_type = Column(String(20), nullable=False, comment='数据类型(hourly/daily)')
    
    # 降水量
    precipitation = Column(Float, comment='降水量(0.1mm)')
    precipitation_20_08 = Column(Float, comment='20-08时降水量(0.1mm)')
    precipitation_08_20 = Column(Float, comment='08-20时降水量(0.1mm)')
    precipitation_20_20 = Column(Float, comment='20-20时降水量(0.1mm)')
    
    # 降水强度
    precipitation_intensity = Column(String(20), comment='降水强度等级')
    precipitation_max_1h = Column(Float, comment='1小时最大降水量(0.1mm)')
    precipitation_max_10min = Column(Float, comment='10分钟最大降水量(0.1mm)')
    
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')


class WindData(Base):
    """风数据表"""
    __tablename__ = 'wind_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    station_id = Column(String(10), nullable=False, comment='区站号')
    observation_time = Column(DateTime, nullable=False, comment='观测时间')
    data_type = Column(String(20), nullable=False, comment='数据类型(hourly/daily)')
    
    # 2分钟平均风
    wind_direction_2min = Column(String(10), comment='2分钟平均风向')
    wind_speed_2min = Column(Float, comment='2分钟平均风速(0.1m/s)')
    
    # 10分钟平均风
    wind_direction_10min = Column(String(10), comment='10分钟平均风向')
    wind_speed_10min = Column(Float, comment='10分钟平均风速(0.1m/s)')
    
    # 最大风
    wind_speed_max = Column(Float, comment='最大风速(0.1m/s)')
    wind_direction_max = Column(String(10), comment='最大风速时风向')
    wind_speed_max_time = Column(String(10), comment='最大风速时间')
    
    # 极大风
    wind_speed_extreme = Column(Float, comment='极大风速(0.1m/s)')
    wind_direction_extreme = Column(String(10), comment='极大风速时风向')
    wind_speed_extreme_time = Column(String(10), comment='极大风速时间')
    
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')


def create_tables(engine):
    """创建所有表"""
    Base.metadata.create_all(engine)


def get_session(database_url):
    """获取数据库会话"""
    engine = create_engine(database_url, echo=False)
    create_tables(engine)
    Session = sessionmaker(bind=engine)
    return Session(), engine
